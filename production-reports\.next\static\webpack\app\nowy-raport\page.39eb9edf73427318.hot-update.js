"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/nowy-raport/page",{

/***/ "(app-pages-browser)/./src/app/nowy-raport/page.tsx":
/*!**************************************!*\
  !*** ./src/app/nowy-raport/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NowyRaport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction NowyRaport() {\n    var _produkty_find;\n    _s();\n    const [produkty, setProdukty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [maszyny, setMaszyny] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pracownicy, setPracownicy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [surowce, setSurowce] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [materialy, setMaterialy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const initialFormData = {\n        produkt_id: \"\",\n        kod_handlowy: \"\",\n        nazwa_produktu: \"\",\n        nr_zamowienia: \"\",\n        maszyna_id: \"\",\n        pracownik_id: \"\",\n        uwagi: \"\",\n        waga_input: \"\"\n    };\n    const initialMetrazEntries = [\n        {\n            metraz: \"\",\n            ilosc: \"\"\n        },\n        {\n            metraz: \"0\",\n            ilosc: \"\"\n        },\n        {\n            metraz: \"0\",\n            ilosc: \"\"\n        },\n        {\n            metraz: \"0\",\n            ilosc: \"\"\n        }\n    ];\n    const initialSurowceEntries = Array(5).fill(null).map((_, index)=>({\n            percentage: index === 0 ? \"100\" : \"0\",\n            surowiec_id: \"\",\n            calculated_usage: 0,\n            szarza: \"\",\n            zuzycie_surowca: \"0.00\",\n            odpad_nieuzytkowy: \"0.00\"\n        }));\n    const initialMaterialyEntries = Array(5).fill(null).map(()=>({\n            percentage: \"0\",\n            material_id: \"\",\n            calculated_usage: 0,\n            status: \"\",\n            zuzycie: \"0.00\",\n            odpad: \"0.00\"\n        }));\n    const initialCzasPracy = {\n        czas_pracy_maszyny: \"0\",\n        czas_pracy_pracownika: \"0\",\n        czas_rozgrzania_maszyny: \"0\",\n        czas_dogrzania_maszyny: \"0\",\n        przebudowa: \"0\",\n        inne: \"0\"\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [metrazEntries, setMetrazEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialMetrazEntries);\n    const [surowceEntries, setSurowceEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialSurowceEntries);\n    const [materialyEntries, setMaterialyEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialMaterialyEntries);\n    const [czasPracy, setCzasPracy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCzasPracy);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NowyRaport.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"NowyRaport.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] = await Promise.all([\n                fetch(\"/api/produkty-direct\"),\n                fetch(\"/api/maszyny\"),\n                fetch(\"/api/pracownicy\"),\n                fetch(\"/api/surowce\"),\n                fetch(\"/api/materialy\")\n            ]);\n            const produktyData = await produktyRes.json();\n            setProdukty(produktyData);\n            setMaszyny(await maszynyRes.json());\n            setPracownicy(await pracownicyRes.json());\n            setSurowce(await surowceRes.json());\n            setMaterialy(await materialyRes.json());\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania danych:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmitting(true);\n        try {\n            var _metrazEntries_;\n            const reportData = {\n                produkt_id: parseInt(formData.produkt_id),\n                metraz_rolek: parseFloat((_metrazEntries_ = metrazEntries[0]) === null || _metrazEntries_ === void 0 ? void 0 : _metrazEntries_.metraz) || 0,\n                ilosc_rolek: metrazEntries.reduce((sum, entry)=>sum + (parseInt(entry.ilosc) || 0), 0),\n                nr_zamowienia: formData.nr_zamowienia,\n                waga: parseFloat(formData.waga_input) || null,\n                surowce: surowceEntries.filter((s)=>s.surowiec_id).map((s)=>({\n                        surowiec_id: parseInt(s.surowiec_id),\n                        zuzyty_surowiec: parseFloat(s.zuzycie_surowca),\n                        odpad_surowiec: parseFloat(s.odpad_nieuzytkowy),\n                        szarza: s.szarza\n                    })),\n                materialy: materialyEntries.filter((m)=>m.material_id).map((m)=>({\n                        material_id: parseInt(m.material_id),\n                        zuzyty_material: parseFloat(m.zuzycie),\n                        odpad_material: parseFloat(m.odpad),\n                        szarza: m.status\n                    })),\n                maszyna_id: parseInt(formData.maszyna_id),\n                pracownik_id: parseInt(formData.pracownik_id),\n                czas_pracy_maszyny: parseFloat(czasPracy.czas_pracy_maszyny),\n                czas_pracy_pracownika: parseFloat(czasPracy.czas_pracy_pracownika),\n                uwagi: formData.uwagi\n            };\n            const response = await fetch(\"/api/raporty-direct\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(reportData)\n            });\n            if (response.ok) {\n                alert(\"Raport został dodany pomyślnie!\");\n                handleClearForm();\n            } else {\n                const errorData = await response.json();\n                alert(\"Błąd podczas dodawania raportu: \".concat(errorData.error));\n            }\n        } catch (error) {\n            console.error(\"Błąd:\", error);\n            alert(\"Wystąpił błąd podczas wysyłania formularza.\");\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NowyRaport.useEffect\": ()=>{\n            const weight = parseFloat(formData.waga_input) || 0;\n            const totalMeters = calculateTotalMeters();\n            const newSurowceEntries = surowceEntries.map({\n                \"NowyRaport.useEffect.newSurowceEntries\": (entry)=>({\n                        ...entry,\n                        calculated_usage: calculateUsage(weight, parseFloat(entry.percentage) || 0, totalMeters)\n                    })\n            }[\"NowyRaport.useEffect.newSurowceEntries\"]);\n            setSurowceEntries(newSurowceEntries);\n            const newMaterialEntries = materialyEntries.map({\n                \"NowyRaport.useEffect.newMaterialEntries\": (entry)=>({\n                        ...entry,\n                        calculated_usage: calculateMaterialUsage(parseFloat(entry.percentage) || 0, totalMeters)\n                    })\n            }[\"NowyRaport.useEffect.newMaterialEntries\"]);\n            setMaterialyEntries(newMaterialEntries);\n        }\n    }[\"NowyRaport.useEffect\"], [\n        formData.waga_input,\n        metrazEntries\n    ]);\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        if (name === \"produkt_id\" && value) {\n            const selectedProduct = produkty.find((p)=>p.id === parseInt(value));\n            if (selectedProduct) {\n                setFormData((prev)=>({\n                        ...prev,\n                        nazwa_produktu: selectedProduct.nazwa || \"\"\n                    }));\n                const newMetrazEntries = [\n                    ...metrazEntries\n                ];\n                newMetrazEntries[0] = {\n                    ...newMetrazEntries[0],\n                    metraz: selectedProduct.metraz.toString()\n                };\n                setMetrazEntries(newMetrazEntries);\n                if (selectedProduct.surowiec) {\n                    const matchingSurowiec = surowce.find((s)=>s.nazwa.trim() === selectedProduct.surowiec.trim());\n                    if (matchingSurowiec) {\n                        const newEntries = [\n                            ...surowceEntries\n                        ];\n                        newEntries[0] = {\n                            ...newEntries[0],\n                            surowiec_id: matchingSurowiec.id.toString()\n                        };\n                        setSurowceEntries(newEntries);\n                    }\n                }\n                const totalMeters = newMetrazEntries.reduce((sum, entry)=>{\n                    const metraz = parseFloat(entry.metraz) || 0;\n                    const ilosc = parseFloat(entry.ilosc) || 0;\n                    return sum + metraz * ilosc;\n                }, 0);\n                const newMaterialEntries = [\n                    ...initialMaterialyEntries\n                ];\n                const materialFields = [\n                    {\n                        name: selectedProduct.material,\n                        percent: selectedProduct.material_percent\n                    },\n                    {\n                        name: selectedProduct.material1,\n                        percent: selectedProduct.material1_percent\n                    },\n                    {\n                        name: selectedProduct.material2,\n                        percent: selectedProduct.material2_percent\n                    },\n                    {\n                        name: selectedProduct.material3,\n                        percent: selectedProduct.material3_percent\n                    },\n                    {\n                        name: selectedProduct.material4,\n                        percent: selectedProduct.material4_percent\n                    },\n                    {\n                        name: selectedProduct.material5,\n                        percent: selectedProduct.material5_percent\n                    }\n                ];\n                let entryIndex = 0;\n                materialFields.forEach((materialField)=>{\n                    if (materialField.name && entryIndex < newMaterialEntries.length) {\n                        const matchingMaterial = materialy.find((m)=>m.nazwa.trim() === materialField.name.trim());\n                        if (matchingMaterial) {\n                            const materialPercentage = materialField.percent || 0;\n                            newMaterialEntries[entryIndex] = {\n                                ...newMaterialEntries[entryIndex],\n                                material_id: matchingMaterial.id.toString(),\n                                percentage: materialPercentage.toString(),\n                                calculated_usage: calculateMaterialUsage(materialPercentage, totalMeters)\n                            };\n                            entryIndex++;\n                        }\n                    }\n                });\n                setMaterialyEntries(newMaterialEntries);\n            }\n        }\n    };\n    // Calculate usage based on weight, percentage, and total meters (result in kg)\n    const calculateUsage = (weight, percentage, totalMeters)=>{\n        // Convert from grams to kilograms\n        return weight * (percentage / 100) * totalMeters / 1000;\n    };\n    // Calculate material usage based on total meters and percentage (no weight)\n    const calculateMaterialUsage = (percentage, totalMeters)=>{\n        return totalMeters * (percentage / 100);\n    };\n    // Calculate total meters produced\n    const calculateTotalMeters = ()=>{\n        return metrazEntries.reduce((sum, entry)=>{\n            const metraz = parseFloat(entry.metraz) || 0;\n            const ilosc = parseFloat(entry.ilosc) || 0;\n            return sum + metraz * ilosc;\n        }, 0);\n    };\n    // Calculate efficiency percentage based on wydajnosc (per 8h shift), meters produced, and time used\n    const calculateEfficiencyPercentage = ()=>{\n        const selectedProduct = produkty.find((p)=>p.id === parseInt(formData.produkt_id));\n        if (!selectedProduct || !selectedProduct.wydajnosc) {\n            return 0;\n        }\n        const totalMeters = calculateTotalMeters();\n        const timeUsedMinutes = parseFloat(czasPracy.czas_pracy_maszyny) || 0;\n        const timeUsedHours = timeUsedMinutes / 60; // Convert minutes to hours\n        if (timeUsedMinutes === 0) return 0;\n        // wydajnosc is per 8-hour shift, so calculate expected production for the time used\n        const expectedMetersForTimeUsed = selectedProduct.wydajnosc * timeUsedHours / 8;\n        // Calculate efficiency percentage: actual vs expected for the time period\n        const efficiency = totalMeters / expectedMetersForTimeUsed * 100;\n        return Math.min(efficiency, 999); // Allow over 100% efficiency\n    };\n    const handleSurowiecChange = (index, field, value)=>{\n        const newEntries = [\n            ...surowceEntries\n        ];\n        newEntries[index][field] = value;\n        setSurowceEntries(newEntries);\n    };\n    const handleMetrazChange = (index, field, value)=>{\n        const newEntries = [\n            ...metrazEntries\n        ];\n        newEntries[index] = {\n            ...newEntries[index],\n            [field]: value\n        };\n        setMetrazEntries(newEntries);\n    };\n    const handleMaterialChange = (index, field, value)=>{\n        const newEntries = [\n            ...materialyEntries\n        ];\n        newEntries[index][field] = value;\n        setMaterialyEntries(newEntries);\n    };\n    const handleCzasPracyChange = (field, value)=>{\n        const updatedCzasPracy = {\n            ...czasPracy,\n            [field]: value\n        };\n        const numValue = parseFloat(value) || 0;\n        if (field === \"czas_pracy_pracownika\" && numValue > 480) {\n            alert(\"Czas pracy pracownika nie może przekroczyć 480 minut (8 godzin)\");\n            return;\n        }\n        const { czas_pracy_pracownika, czas_pracy_maszyny, czas_rozgrzania_maszyny, czas_dogrzania_maszyny, przebudowa, inne } = updatedCzasPracy;\n        const machineTimeSum = parseFloat(czas_pracy_maszyny) + parseFloat(czas_rozgrzania_maszyny) + parseFloat(czas_dogrzania_maszyny) + parseFloat(przebudowa) + parseFloat(inne);\n        const workerTime = parseFloat(czas_pracy_pracownika);\n        if (machineTimeSum > workerTime && workerTime > 0) {\n            alert(\"Suma czas\\xf3w maszyny (\".concat(machineTimeSum, \" min) nie może przekroczyć czasu pracy pracownika (\").concat(workerTime, \" min)\"));\n            return;\n        }\n        setCzasPracy(updatedCzasPracy);\n    };\n    const handleClearForm = ()=>{\n        setFormData(initialFormData);\n        setSurowceEntries(initialSurowceEntries);\n        setMetrazEntries(initialMetrazEntries);\n        setMaterialyEntries(initialMaterialyEntries);\n        setCzasPracy(initialCzasPracy);\n    };\n    const totalMeters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NowyRaport.useMemo[totalMeters]\": ()=>calculateTotalMeters()\n    }[\"NowyRaport.useMemo[totalMeters]\"], [\n        metrazEntries\n    ]);\n    const efficiencyPercentage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NowyRaport.useMemo[efficiencyPercentage]\": ()=>calculateEfficiencyPercentage()\n    }[\"NowyRaport.useMemo[efficiencyPercentage]\"], [\n        produkty,\n        formData.produkt_id,\n        totalMeters,\n        czasPracy.czas_pracy_maszyny\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-xl p-8 border border-gray-700 shadow-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xl text-white\",\n                            children: \"Ładowanie danych...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n            lineNumber: 488,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-4000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[1600px] mx-auto relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 mb-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: \"\\uD83D\\uDCCA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                            children: \"06-A-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Production Report System\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\",\n                                                    className: \"group bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white px-6 py-3 rounded-xl transition-all duration-300 border border-gray-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"transform group-hover:-translate-x-1 transition-transform duration-300\",\n                                                                children: \"←\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Powr\\xf3t\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin-db\",\n                                                    className: \"group bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white px-6 py-3 rounded-xl transition-all duration-300 border border-purple-400 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"transform group-hover:rotate-12 transition-transform duration-300\",\n                                                                children: \"\\uD83D\\uDDC4️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Zarządzaj bazą danych\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 bg-green-900 bg-opacity-50 px-4 py-2 rounded-full border border-green-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-300 font-semibold text-sm\",\n                                                    children: \"System Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: new Date().toLocaleTimeString(\"pl-PL\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-3 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"ℹ️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-center bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-6 pb-3 border-b border-gray-600\",\n                                            children: \"INFORMACJE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Odbiorca\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner\",\n                                                            placeholder: \"Wprowadź odbiorcę...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Wydajność [m/8h]\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: ((_produkty_find = produkty.find((p)=>p.id === parseInt(formData.produkt_id))) === null || _produkty_find === void 0 ? void 0 : _produkty_find.wydajnosc) || \"\",\n                                                                    readOnly: true,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-600 to-gray-500 border border-gray-500 rounded-xl text-white text-sm cursor-not-allowed shadow-inner\",\n                                                                    placeholder: \"Automatycznie wypełniane...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"\\uD83D\\uDD12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 17\n                                                }, this),\n                                                formData.produkt_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 p-4 bg-gradient-to-br from-gray-700 to-gray-600 rounded-xl border border-gray-500 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-2 text-sm font-medium text-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Efektywność\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 620,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-800 px-3 py-1 rounded-full border border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\",\n                                                                        children: [\n                                                                            efficiencyPercentage.toFixed(1),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-full bg-gray-800 rounded-full h-4 border border-gray-600 shadow-inner overflow-hidden\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 rounded-full transition-all duration-1000 ease-out relative overflow-hidden \".concat(efficiencyPercentage >= 80 ? \"bg-gradient-to-r from-green-400 to-green-500\" : efficiencyPercentage >= 60 ? \"bg-gradient-to-r from-yellow-400 to-orange-400\" : \"bg-gradient-to-r from-red-400 to-red-500\"),\n                                                                style: {\n                                                                    width: \"\".concat(Math.min(efficiencyPercentage, 100), \"%\")\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-white opacity-20 animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-3 text-xs text-gray-300 text-center bg-gray-800 bg-opacity-50 p-2 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        totalMeters.toFixed(1),\n                                                                        \"m \\xf7\",\n                                                                        \" \",\n                                                                        (()=>{\n                                                                            const selectedProduct = produkty.find((p)=>p.id === parseInt(formData.produkt_id));\n                                                                            const timeUsedMinutes = parseFloat(czasPracy.czas_pracy_maszyny) || 0;\n                                                                            const timeUsedHours = timeUsedMinutes / 60;\n                                                                            const expectedForTime = (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.wydajnosc) ? (selectedProduct.wydajnosc * timeUsedHours / 8).toFixed(1) : \"0.0\";\n                                                                            return \"\".concat(expectedForTime, \"m\");\n                                                                        })()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 645,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 mt-1\",\n                                                                    children: [\n                                                                        \"Oczekiwane za\",\n                                                                        \" \",\n                                                                        parseFloat(czasPracy.czas_pracy_maszyny) || 0,\n                                                                        \" min\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4 border-t border-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Kartoteka:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Giza:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Info2:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 682,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 683,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Info3:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 687,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Info4:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Info5:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    id: \"production-form\",\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: \"\\uD83D\\uDCE6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                            children: \"Informacje o produkcie\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 723,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Kod handlowy\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 724,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 725,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    name: \"produkt_id\",\n                                                                    value: formData.produkt_id,\n                                                                    onChange: handleChange,\n                                                                    required: true,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Wybierz produkt...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        produkty.map((produkt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: produkt.id,\n                                                                                children: produkt.kod_handlowy\n                                                                            }, produkt.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 736,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Nazwa produktu\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"nazwa_produktu\",\n                                                                    value: formData.nazwa_produktu,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner\",\n                                                                    placeholder: \"Nazwa produktu...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-purple-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 760,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Nr zam\\xf3wienia, klient, info\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"nr_zamowienia\",\n                                                                    value: formData.nr_zamowienia,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 shadow-inner\",\n                                                                    placeholder: \"Nr zam\\xf3wienia...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: \"⚖️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\",\n                                                            children: \"Parametry produkcji\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Waga [g]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    name: \"waga_input\",\n                                                                    value: formData.waga_input,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-all duration-300 shadow-inner text-center\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Metraż\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: metrazEntries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: entry.metraz,\n                                                                            onChange: (e)=>handleMetrazChange(index, \"metraz\", e.target.value),\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner text-center\",\n                                                                            placeholder: \"0.00\"\n                                                                        }, \"metraz-\".concat(index), false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 811,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 809,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 804,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 829,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Ilość [szt]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 830,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: metrazEntries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: entry.ilosc,\n                                                                            onChange: (e)=>handleMetrazChange(index, \"ilosc\", e.target.value),\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner text-center\",\n                                                                            placeholder: \"0\"\n                                                                        }, \"ilosc-\".concat(index), false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 834,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-purple-400 rounded-full animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 851,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Suma metr\\xf3w wyprodukowanych\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 852,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-br from-gray-700 to-gray-600 border border-gray-500 rounded-xl p-4 text-center shadow-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-3xl font-bold\",\n                                                                                    children: totalMeters.toFixed(2)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 856,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xl font-semibold ml-2\",\n                                                                                    children: \"m\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 859,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 855,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full bg-gray-800 rounded-full h-2 mt-2 overflow-hidden\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 862,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 861,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 text-xs text-gray-300 text-center bg-gray-800 bg-opacity-50 p-3 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold text-gray-200 mb-2\",\n                                                                            children: \"Szczeg\\xf3ły kalkulacji:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 866,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        metrazEntries.map((entry, index)=>{\n                                                                            const metraz = parseFloat(entry.metraz) || 0;\n                                                                            const ilosc = parseFloat(entry.ilosc) || 0;\n                                                                            const total = metraz * ilosc;\n                                                                            return total > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center py-1 border-b border-gray-700 last:border-b-0\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-400\",\n                                                                                        children: [\n                                                                                            \"Pozycja \",\n                                                                                            index + 1,\n                                                                                            \":\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                        lineNumber: 878,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white font-mono\",\n                                                                                        children: [\n                                                                                            metraz,\n                                                                                            \"m \\xd7 \",\n                                                                                            ilosc,\n                                                                                            \"szt =\",\n                                                                                            \" \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-green-400\",\n                                                                                                children: [\n                                                                                                    total.toFixed(2),\n                                                                                                    \"m\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                                lineNumber: 883,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                        lineNumber: 881,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 874,\n                                                                                columnNumber: 27\n                                                                            }, this) : null;\n                                                                        })\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg mr-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-xl\",\n                                                                children: \"\\uD83E\\uDDF1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent\",\n                                                            children: \"SUROWCE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Procent [%]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Surowiec\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 912,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Szarża\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 917,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: [\n                                                                    \"Zużycie [kg]\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 924,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"(waga \\xd7 metry \\xd7 % \\xf7 1000)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Opad użytkowy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Odpad nieużytkowy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 936,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 17\n                                                }, this),\n                                                surowceEntries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.1\",\n                                                                    value: entry.percentage,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"percentage\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: entry.surowiec_id,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"surowiec_id\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Wybierz surowiec\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        surowce.map((surowiec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: surowiec.id,\n                                                                                children: surowiec.nazwa\n                                                                            }, surowiec.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 977,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: entry.szarza,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"szarza\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"Szarża\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.001\",\n                                                                    value: entry.calculated_usage.toFixed(3),\n                                                                    readOnly: true,\n                                                                    className: \"w-full p-1 bg-gray-600 border border-gray-600 rounded text-white text-xs cursor-not-allowed\",\n                                                                    placeholder: \"0.000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 999,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.zuzycie_surowca,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"zuzycie_surowca\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1011,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.odpad_nieuzytkowy,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"odpad_nieuzytkowy\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1029,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1028,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg mr-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-xl\",\n                                                                children: \"\\uD83D\\uDD27\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1052,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1051,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent\",\n                                                            children: \"MATERIAŁY\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1054,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1050,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Procent [%]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1060,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1059,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Materiał\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Szarża\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1070,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1069,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: [\n                                                                    \"Zużycie\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1077,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"(metry \\xd7 %)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1078,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1075,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1074,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Odpad użytkowy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1082,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Odpad nieużytkowy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1086,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1058,\n                                                    columnNumber: 17\n                                                }, this),\n                                                materialyEntries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.1\",\n                                                                    value: entry.percentage,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"percentage\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1097,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1096,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: entry.material_id,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"material_id\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Wybierz materiał\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1126,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        materialy.map((material)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: material.id,\n                                                                                children: material.nazwa\n                                                                            }, material.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 1128,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1115,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1114,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: entry.status,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"status\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"Szarża\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1137,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1136,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.calculated_usage.toFixed(2),\n                                                                    readOnly: true,\n                                                                    className: \"w-full p-1 bg-gray-600 border border-gray-600 rounded text-white text-xs cursor-not-allowed\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1150,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.zuzycie,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"zuzycie\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1162,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1161,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.odpad,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"odpad\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1176,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1049,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: \"\\uD83D\\uDCDD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-lg font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent\",\n                                                            children: \"Uwagi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1197,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    name: \"uwagi\",\n                                                    value: formData.uwagi,\n                                                    onChange: handleChange,\n                                                    rows: 4,\n                                                    className: \"w-full p-4 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-all duration-300 shadow-inner resize-none\",\n                                                    placeholder: \"Dodatkowe informacje o produkcji, uwagi, komentarze...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1201,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"⏱️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                lineNumber: 1217,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-center bg-gradient-to-r from-green-400 to-teal-400 bg-clip-text text-transparent mb-6 pb-3 border-b border-gray-600\",\n                                            children: \"CZAS PRACY\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-800 bg-opacity-30 p-4 rounded-xl border border-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-gray-200 mb-4 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-3 h-3 bg-blue-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1229,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"WYB\\xd3R MASZYNY I PRACOWNIKA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1230,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1228,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1235,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Maszyna\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1236,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-400\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1237,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1234,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"maszyna_id\",\n                                                                            value: formData.maszyna_id,\n                                                                            onChange: handleChange,\n                                                                            required: true,\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Wybierz maszynę...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1246,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                maszyny.map((maszyna)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: maszyna.id,\n                                                                                        children: maszyna.nazwa\n                                                                                    }, maszyna.id, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                        lineNumber: 1248,\n                                                                                        columnNumber: 27\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1239,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1233,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1257,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Pracownik\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1258,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-400\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1259,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1256,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"pracownik_id\",\n                                                                            value: formData.pracownik_id,\n                                                                            onChange: handleChange,\n                                                                            required: true,\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Wybierz pracownika...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1268,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                pracownicy.map((pracownik)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: pracownik.id,\n                                                                                        children: [\n                                                                                            pracownik.numer,\n                                                                                            \" - \",\n                                                                                            pracownik.imie,\n                                                                                            \" \",\n                                                                                            pracownik.nazwisko\n                                                                                        ]\n                                                                                    }, pracownik.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                        lineNumber: 1270,\n                                                                                        columnNumber: 27\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1261,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1255,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1232,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-900 bg-opacity-20 p-4 rounded-xl border border-green-500 border-opacity-30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-green-300 mb-4 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-3 h-3 bg-green-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1283,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CZAS PRACY PRACOWNIKA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1284,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1282,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1288,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Czas pracy pracownika (min)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1289,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1290,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400 ml-auto\",\n                                                                            children: \"Max: 480 min\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1291,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1287,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"1\",\n                                                                    min: \"0\",\n                                                                    max: \"480\",\n                                                                    value: czasPracy.czas_pracy_pracownika,\n                                                                    onChange: (e)=>handleCzasPracyChange(\"czas_pracy_pracownika\", e.target.value),\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-green-800 to-green-700 border border-green-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner\",\n                                                                    placeholder: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1295,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1286,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-900 bg-opacity-20 p-4 rounded-xl border border-yellow-500 border-opacity-30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-yellow-300 mb-4 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-3 h-3 bg-yellow-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1316,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CZASY MASZYNY\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1317,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1315,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1322,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Czas pracy maszyny (min)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1323,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1321,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            step: \"1\",\n                                                                            min: \"0\",\n                                                                            value: czasPracy.czas_pracy_maszyny,\n                                                                            onChange: (e)=>handleCzasPracyChange(\"czas_pracy_maszyny\", e.target.value),\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-yellow-800 to-yellow-700 border border-yellow-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-all duration-300 shadow-inner\",\n                                                                            placeholder: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1325,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1320,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"w-2 h-2 bg-orange-400 rounded-full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1344,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Rozgrzanie (min)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1345,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1343,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    step: \"1\",\n                                                                                    min: \"0\",\n                                                                                    value: czasPracy.czas_rozgrzania_maszyny,\n                                                                                    onChange: (e)=>handleCzasPracyChange(\"czas_rozgrzania_maszyny\", e.target.value),\n                                                                                    className: \"w-full p-3 bg-gradient-to-r from-orange-800 to-orange-700 border border-orange-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-orange-400 focus:border-orange-400 transition-all duration-300 shadow-inner\",\n                                                                                    placeholder: \"0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1347,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1342,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"w-2 h-2 bg-purple-400 rounded-full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1365,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Dogrzanie (min)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1366,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1364,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    step: \"1\",\n                                                                                    min: \"0\",\n                                                                                    value: czasPracy.czas_dogrzania_maszyny,\n                                                                                    onChange: (e)=>handleCzasPracyChange(\"czas_dogrzania_maszyny\", e.target.value),\n                                                                                    className: \"w-full p-3 bg-gradient-to-r from-purple-800 to-purple-700 border border-purple-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 shadow-inner\",\n                                                                                    placeholder: \"0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1368,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1363,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1341,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"w-2 h-2 bg-pink-400 rounded-full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1388,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Przebudowa (min)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1389,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1387,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    step: \"1\",\n                                                                                    min: \"0\",\n                                                                                    value: czasPracy.przebudowa,\n                                                                                    onChange: (e)=>handleCzasPracyChange(\"przebudowa\", e.target.value),\n                                                                                    className: \"w-full p-3 bg-gradient-to-r from-pink-800 to-pink-700 border border-pink-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-pink-400 focus:border-pink-400 transition-all duration-300 shadow-inner\",\n                                                                                    placeholder: \"0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1391,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1386,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"w-2 h-2 bg-indigo-400 rounded-full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1406,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Inne (min)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1407,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1405,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    step: \"1\",\n                                                                                    min: \"0\",\n                                                                                    value: czasPracy.inne,\n                                                                                    onChange: (e)=>handleCzasPracyChange(\"inne\", e.target.value),\n                                                                                    className: \"w-full p-3 bg-gradient-to-r from-indigo-800 to-indigo-700 border border-indigo-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-all duration-300 shadow-inner\",\n                                                                                    placeholder: \"0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1409,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1404,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1385,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1319,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-6 border-t border-gray-600 space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: handleClearForm,\n                                                            className: \"group w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center justify-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"transform group-hover:rotate-12 transition-transform duration-300\",\n                                                                        children: \"\\uD83D\\uDDD1️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1432,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Wyczyść\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1435,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1431,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1426,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            className: \"group w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-500 hover:to-orange-500 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center justify-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"transform group-hover:scale-110 transition-transform duration-300\",\n                                                                        children: \"\\uD83D\\uDCBE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1444,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Zapamiętaj (1)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1447,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1443,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1439,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            className: \"group w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-500 hover:to-orange-500 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center justify-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"transform group-hover:scale-110 transition-transform duration-300\",\n                                                                        children: \"\\uD83D\\uDCBE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1456,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Zapamiętaj (2)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1459,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1455,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1451,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            form: \"production-form\",\n                                                            disabled: submitting,\n                                                            className: \"group w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 disabled:from-gray-600 disabled:to-gray-700 text-white py-4 px-4 rounded-xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none\",\n                                                            children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1471,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Zapisywanie...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1472,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1470,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center justify-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"transform group-hover:scale-110 transition-transform duration-300\",\n                                                                        children: \"✅\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1476,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"ZAPISZ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1479,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1475,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1463,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                lineNumber: 1214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n        lineNumber: 500,\n        columnNumber: 5\n    }, this);\n}\n_s(NowyRaport, \"qSuRAeUZLwaZBC84ic7d1hmlHzw=\");\n_c = NowyRaport;\nvar _c;\n$RefreshReg$(_c, \"NowyRaport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/nowy-raport/page.tsx\n"));

/***/ })

});