"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/nowy-raport/page",{

/***/ "(app-pages-browser)/./src/app/nowy-raport/page.tsx":
/*!**************************************!*\
  !*** ./src/app/nowy-raport/page.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NowyRaport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction NowyRaport() {\n    var _produkty_find;\n    _s();\n    const [produkty, setProdukty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [maszyny, setMaszyny] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [pracownicy, setPracownicy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [surowce, setSurowce] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [materialy, setMaterialy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [submitting, setSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const initialFormData = {\n        produkt_id: \"\",\n        kod_handlowy: \"\",\n        nazwa_produktu: \"\",\n        nr_zamowienia: \"\",\n        maszyna_id: \"\",\n        pracownik_id: \"\",\n        uwagi: \"\",\n        waga_input: \"\"\n    };\n    const initialMetrazEntries = [\n        {\n            metraz: \"\",\n            ilosc: \"\"\n        },\n        {\n            metraz: \"0\",\n            ilosc: \"\"\n        },\n        {\n            metraz: \"0\",\n            ilosc: \"\"\n        },\n        {\n            metraz: \"0\",\n            ilosc: \"\"\n        }\n    ];\n    const initialSurowceEntries = Array(5).fill(null).map((_, index)=>({\n            percentage: index === 0 ? \"100\" : \"0\",\n            surowiec_id: \"\",\n            calculated_usage: 0,\n            szarza: \"\",\n            zuzycie_surowca: \"0.00\",\n            odpad_nieuzytkowy: \"0.00\"\n        }));\n    const initialMaterialyEntries = Array(5).fill(null).map(()=>({\n            percentage: \"0\",\n            material_id: \"\",\n            calculated_usage: 0,\n            status: \"\",\n            zuzycie: \"0.00\",\n            odpad: \"0.00\"\n        }));\n    const initialCzasPracy = {\n        czas_pracy_maszyny: \"0\",\n        czas_pracy_pracownika: \"0\",\n        czas_rozgrzania_maszyny: \"0\",\n        czas_dogrzania_maszyny: \"0\",\n        przebudowa: \"0\",\n        inne: \"0\"\n    };\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const [metrazEntries, setMetrazEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialMetrazEntries);\n    const [surowceEntries, setSurowceEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialSurowceEntries);\n    const [materialyEntries, setMaterialyEntries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialMaterialyEntries);\n    const [czasPracy, setCzasPracy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialCzasPracy);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NowyRaport.useEffect\": ()=>{\n            fetchData();\n        }\n    }[\"NowyRaport.useEffect\"], []);\n    const fetchData = async ()=>{\n        try {\n            const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] = await Promise.all([\n                fetch(\"/api/produkty-direct\"),\n                fetch(\"/api/maszyny\"),\n                fetch(\"/api/pracownicy\"),\n                fetch(\"/api/surowce\"),\n                fetch(\"/api/materialy\")\n            ]);\n            const produktyData = await produktyRes.json();\n            setProdukty(produktyData);\n            setMaszyny(await maszynyRes.json());\n            setPracownicy(await pracownicyRes.json());\n            setSurowce(await surowceRes.json());\n            setMaterialy(await materialyRes.json());\n        } catch (error) {\n            console.error(\"Błąd podczas ładowania danych:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setSubmitting(true);\n        try {\n            var _metrazEntries_;\n            const reportData = {\n                produkt_id: parseInt(formData.produkt_id),\n                metraz_rolek: parseFloat((_metrazEntries_ = metrazEntries[0]) === null || _metrazEntries_ === void 0 ? void 0 : _metrazEntries_.metraz) || 0,\n                ilosc_rolek: metrazEntries.reduce((sum, entry)=>sum + (parseInt(entry.ilosc) || 0), 0),\n                nr_zamowienia: formData.nr_zamowienia,\n                waga: parseFloat(formData.waga_input) || null,\n                surowce: surowceEntries.filter((s)=>s.surowiec_id).map((s)=>({\n                        surowiec_id: parseInt(s.surowiec_id),\n                        zuzyty_surowiec: parseFloat(s.opad_uzytkowy),\n                        odpad_surowiec: parseFloat(s.odpad_nieuzytkowy),\n                        szarza: s.szarza\n                    })),\n                materialy: materialyEntries.filter((m)=>m.material_id).map((m)=>({\n                        material_id: parseInt(m.material_id),\n                        zuzyty_material: parseFloat(m.zuzycie),\n                        odpad_material: parseFloat(m.odpad),\n                        szarza: m.status\n                    })),\n                maszyna_id: parseInt(formData.maszyna_id),\n                pracownik_id: parseInt(formData.pracownik_id),\n                czas_pracy_maszyny: parseFloat(czasPracy.czas_pracy_maszyny),\n                czas_pracy_pracownika: parseFloat(czasPracy.czas_pracy_pracownika),\n                uwagi: formData.uwagi\n            };\n            const response = await fetch(\"/api/raporty-direct\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(reportData)\n            });\n            if (response.ok) {\n                alert(\"Raport został dodany pomyślnie!\");\n                handleClearForm();\n            } else {\n                const errorData = await response.json();\n                alert(\"Błąd podczas dodawania raportu: \".concat(errorData.error));\n            }\n        } catch (error) {\n            console.error(\"Błąd:\", error);\n            alert(\"Wystąpił błąd podczas wysyłania formularza.\");\n        } finally{\n            setSubmitting(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NowyRaport.useEffect\": ()=>{\n            const weight = parseFloat(formData.waga_input) || 0;\n            const totalMeters = calculateTotalMeters();\n            const newSurowceEntries = surowceEntries.map({\n                \"NowyRaport.useEffect.newSurowceEntries\": (entry)=>({\n                        ...entry,\n                        calculated_usage: calculateUsage(weight, parseFloat(entry.percentage) || 0, totalMeters)\n                    })\n            }[\"NowyRaport.useEffect.newSurowceEntries\"]);\n            setSurowceEntries(newSurowceEntries);\n            const newMaterialEntries = materialyEntries.map({\n                \"NowyRaport.useEffect.newMaterialEntries\": (entry)=>({\n                        ...entry,\n                        calculated_usage: calculateMaterialUsage(parseFloat(entry.percentage) || 0, totalMeters)\n                    })\n            }[\"NowyRaport.useEffect.newMaterialEntries\"]);\n            setMaterialyEntries(newMaterialEntries);\n        }\n    }[\"NowyRaport.useEffect\"], [\n        formData.waga_input,\n        metrazEntries\n    ]);\n    const handleChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n        if (name === \"produkt_id\" && value) {\n            const selectedProduct = produkty.find((p)=>p.id === parseInt(value));\n            if (selectedProduct) {\n                setFormData((prev)=>({\n                        ...prev,\n                        nazwa_produktu: selectedProduct.nazwa || \"\"\n                    }));\n                const newMetrazEntries = [\n                    ...metrazEntries\n                ];\n                newMetrazEntries[0] = {\n                    ...newMetrazEntries[0],\n                    metraz: selectedProduct.metraz.toString()\n                };\n                setMetrazEntries(newMetrazEntries);\n                if (selectedProduct.surowiec) {\n                    const matchingSurowiec = surowce.find((s)=>s.nazwa.trim() === selectedProduct.surowiec.trim());\n                    if (matchingSurowiec) {\n                        const newEntries = [\n                            ...surowceEntries\n                        ];\n                        newEntries[0] = {\n                            ...newEntries[0],\n                            surowiec_id: matchingSurowiec.id.toString()\n                        };\n                        setSurowceEntries(newEntries);\n                    }\n                }\n                const totalMeters = newMetrazEntries.reduce((sum, entry)=>{\n                    const metraz = parseFloat(entry.metraz) || 0;\n                    const ilosc = parseFloat(entry.ilosc) || 0;\n                    return sum + metraz * ilosc;\n                }, 0);\n                const newMaterialEntries = [\n                    ...initialMaterialyEntries\n                ];\n                const materialFields = [\n                    {\n                        name: selectedProduct.material,\n                        percent: selectedProduct.material_percent\n                    },\n                    {\n                        name: selectedProduct.material1,\n                        percent: selectedProduct.material1_percent\n                    },\n                    {\n                        name: selectedProduct.material2,\n                        percent: selectedProduct.material2_percent\n                    },\n                    {\n                        name: selectedProduct.material3,\n                        percent: selectedProduct.material3_percent\n                    },\n                    {\n                        name: selectedProduct.material4,\n                        percent: selectedProduct.material4_percent\n                    },\n                    {\n                        name: selectedProduct.material5,\n                        percent: selectedProduct.material5_percent\n                    }\n                ];\n                let entryIndex = 0;\n                materialFields.forEach((materialField)=>{\n                    if (materialField.name && entryIndex < newMaterialEntries.length) {\n                        const matchingMaterial = materialy.find((m)=>m.nazwa.trim() === materialField.name.trim());\n                        if (matchingMaterial) {\n                            const materialPercentage = materialField.percent || 0;\n                            newMaterialEntries[entryIndex] = {\n                                ...newMaterialEntries[entryIndex],\n                                material_id: matchingMaterial.id.toString(),\n                                percentage: materialPercentage.toString(),\n                                calculated_usage: calculateMaterialUsage(materialPercentage, totalMeters)\n                            };\n                            entryIndex++;\n                        }\n                    }\n                });\n                setMaterialyEntries(newMaterialEntries);\n            }\n        }\n    };\n    // Calculate usage based on weight, percentage, and total meters (result in kg)\n    const calculateUsage = (weight, percentage, totalMeters)=>{\n        // Convert from grams to kilograms\n        return weight * (percentage / 100) * totalMeters / 1000;\n    };\n    // Calculate material usage based on total meters and percentage (no weight)\n    const calculateMaterialUsage = (percentage, totalMeters)=>{\n        return totalMeters * (percentage / 100);\n    };\n    // Calculate total meters produced\n    const calculateTotalMeters = ()=>{\n        return metrazEntries.reduce((sum, entry)=>{\n            const metraz = parseFloat(entry.metraz) || 0;\n            const ilosc = parseFloat(entry.ilosc) || 0;\n            return sum + metraz * ilosc;\n        }, 0);\n    };\n    // Calculate efficiency percentage based on wydajnosc (per 8h shift), meters produced, and time used\n    const calculateEfficiencyPercentage = ()=>{\n        const selectedProduct = produkty.find((p)=>p.id === parseInt(formData.produkt_id));\n        if (!selectedProduct || !selectedProduct.wydajnosc) {\n            return 0;\n        }\n        const totalMeters = calculateTotalMeters();\n        const timeUsedMinutes = parseFloat(czasPracy.czas_pracy_maszyny) || 0;\n        const timeUsedHours = timeUsedMinutes / 60; // Convert minutes to hours\n        if (timeUsedMinutes === 0) return 0;\n        // wydajnosc is per 8-hour shift, so calculate expected production for the time used\n        const expectedMetersForTimeUsed = selectedProduct.wydajnosc * timeUsedHours / 8;\n        // Calculate efficiency percentage: actual vs expected for the time period\n        const efficiency = totalMeters / expectedMetersForTimeUsed * 100;\n        return Math.min(efficiency, 999); // Allow over 100% efficiency\n    };\n    const handleSurowiecChange = (index, field, value)=>{\n        const newEntries = [\n            ...surowceEntries\n        ];\n        newEntries[index][field] = value;\n        setSurowceEntries(newEntries);\n    };\n    const handleMetrazChange = (index, field, value)=>{\n        const newEntries = [\n            ...metrazEntries\n        ];\n        newEntries[index] = {\n            ...newEntries[index],\n            [field]: value\n        };\n        setMetrazEntries(newEntries);\n    };\n    const handleMaterialChange = (index, field, value)=>{\n        const newEntries = [\n            ...materialyEntries\n        ];\n        newEntries[index][field] = value;\n        setMaterialyEntries(newEntries);\n    };\n    const handleCzasPracyChange = (field, value)=>{\n        const updatedCzasPracy = {\n            ...czasPracy,\n            [field]: value\n        };\n        const numValue = parseFloat(value) || 0;\n        if (field === \"czas_pracy_pracownika\" && numValue > 480) {\n            alert(\"Czas pracy pracownika nie może przekroczyć 480 minut (8 godzin)\");\n            return;\n        }\n        const { czas_pracy_pracownika, czas_pracy_maszyny, czas_rozgrzania_maszyny, czas_dogrzania_maszyny, przebudowa, inne } = updatedCzasPracy;\n        const machineTimeSum = parseFloat(czas_pracy_maszyny) + parseFloat(czas_rozgrzania_maszyny) + parseFloat(czas_dogrzania_maszyny) + parseFloat(przebudowa) + parseFloat(inne);\n        const workerTime = parseFloat(czas_pracy_pracownika);\n        if (machineTimeSum > workerTime && workerTime > 0) {\n            alert(\"Suma czas\\xf3w maszyny (\".concat(machineTimeSum, \" min) nie może przekroczyć czasu pracy pracownika (\").concat(workerTime, \" min)\"));\n            return;\n        }\n        setCzasPracy(updatedCzasPracy);\n    };\n    const handleClearForm = ()=>{\n        setFormData(initialFormData);\n        setSurowceEntries(initialSurowceEntries);\n        setMetrazEntries(initialMetrazEntries);\n        setMaterialyEntries(initialMaterialyEntries);\n        setCzasPracy(initialCzasPracy);\n    };\n    const totalMeters = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NowyRaport.useMemo[totalMeters]\": ()=>calculateTotalMeters()\n    }[\"NowyRaport.useMemo[totalMeters]\"], [\n        metrazEntries\n    ]);\n    const efficiencyPercentage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"NowyRaport.useMemo[efficiencyPercentage]\": ()=>calculateEfficiencyPercentage()\n    }[\"NowyRaport.useMemo[efficiencyPercentage]\"], [\n        produkty,\n        formData.produkt_id,\n        totalMeters,\n        czasPracy.czas_pracy_maszyny\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-800 rounded-xl p-8 border border-gray-700 shadow-xl\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xl text-white\",\n                            children: \"Ładowanie danych...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n            lineNumber: 488,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4 relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-4000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                lineNumber: 502,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[1600px] mx-auto relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 mb-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-white font-bold text-lg\",\n                                                        children: \"\\uD83D\\uDCCA\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 514,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                            className: \"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                                                            children: \"06-A-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 518,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm\",\n                                                            children: \"Production Report System\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/\",\n                                                    className: \"group bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white px-6 py-3 rounded-xl transition-all duration-300 border border-gray-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"transform group-hover:-translate-x-1 transition-transform duration-300\",\n                                                                children: \"←\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Powr\\xf3t\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: \"/admin-db\",\n                                                    className: \"group bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white px-6 py-3 rounded-xl transition-all duration-300 border border-purple-400 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"transform group-hover:rotate-12 transition-transform duration-300\",\n                                                                children: \"\\uD83D\\uDDC4️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Zarządzaj bazą danych\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 bg-green-900 bg-opacity-50 px-4 py-2 rounded-full border border-green-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-300 font-semibold text-sm\",\n                                                    children: \"System Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 552,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: new Date().toLocaleTimeString(\"pl-PL\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-3 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"ℹ️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 572,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 570,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-center bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-6 pb-3 border-b border-gray-600\",\n                                            children: \"INFORMACJE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 575,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 582,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Odbiorca\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 581,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner\",\n                                                            placeholder: \"Wprowadź odbiorcę...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 585,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 594,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Wydajność [m/8h]\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 595,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 593,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: ((_produkty_find = produkty.find((p)=>p.id === parseInt(formData.produkt_id))) === null || _produkty_find === void 0 ? void 0 : _produkty_find.wydajnosc) || \"\",\n                                                                    readOnly: true,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-600 to-gray-500 border border-gray-500 rounded-xl text-white text-sm cursor-not-allowed shadow-inner\",\n                                                                    placeholder: \"Automatycznie wypełniane...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 598,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-gray-400 text-xs\",\n                                                                        children: \"\\uD83D\\uDD12\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 609,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 592,\n                                                    columnNumber: 17\n                                                }, this),\n                                                formData.produkt_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-6 p-4 bg-gradient-to-br from-gray-700 to-gray-600 rounded-xl border border-gray-500 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"flex items-center space-x-2 text-sm font-medium text-gray-200\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 619,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Efektywność\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 620,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gray-800 px-3 py-1 rounded-full border border-gray-600\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-lg font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\",\n                                                                        children: [\n                                                                            efficiencyPercentage.toFixed(1),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 623,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 622,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative w-full bg-gray-800 rounded-full h-4 border border-gray-600 shadow-inner overflow-hidden\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-4 rounded-full transition-all duration-1000 ease-out relative overflow-hidden \".concat(efficiencyPercentage >= 80 ? \"bg-gradient-to-r from-green-400 to-green-500\" : efficiencyPercentage >= 60 ? \"bg-gradient-to-r from-yellow-400 to-orange-400\" : \"bg-gradient-to-r from-red-400 to-red-500\"),\n                                                                style: {\n                                                                    width: \"\".concat(Math.min(efficiencyPercentage, 100), \"%\")\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-white opacity-20 animate-pulse\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 641,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 628,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-3 text-xs text-gray-300 text-center bg-gray-800 bg-opacity-50 p-2 rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-semibold\",\n                                                                    children: [\n                                                                        totalMeters.toFixed(1),\n                                                                        \"m \\xf7\",\n                                                                        \" \",\n                                                                        (()=>{\n                                                                            const selectedProduct = produkty.find((p)=>p.id === parseInt(formData.produkt_id));\n                                                                            const timeUsedMinutes = parseFloat(czasPracy.czas_pracy_maszyny) || 0;\n                                                                            const timeUsedHours = timeUsedMinutes / 60;\n                                                                            const expectedForTime = (selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.wydajnosc) ? (selectedProduct.wydajnosc * timeUsedHours / 8).toFixed(1) : \"0.0\";\n                                                                            return \"\".concat(expectedForTime, \"m\");\n                                                                        })()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 645,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-gray-400 mt-1\",\n                                                                    children: [\n                                                                        \"Oczekiwane za\",\n                                                                        \" \",\n                                                                        parseFloat(czasPracy.czas_pracy_maszyny) || 0,\n                                                                        \" min\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 663,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 644,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 616,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-4 border-t border-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 text-sm text-gray-300\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Kartoteka:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 674,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Giza:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 678,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 679,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 677,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Info2:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 682,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 683,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Info3:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 686,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 687,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 685,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Info4:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 691,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Info5:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 694,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white\",\n                                                                        children: \"-\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 693,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 672,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                lineNumber: 568,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    id: \"production-form\",\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: \"\\uD83D\\uDCE6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 714,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                            children: \"Informacje o produkcie\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 716,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 712,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 723,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Kod handlowy\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 724,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 725,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 722,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    name: \"produkt_id\",\n                                                                    value: formData.produkt_id,\n                                                                    onChange: handleChange,\n                                                                    required: true,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Wybierz produkt...\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 734,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        produkty.map((produkt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: produkt.id,\n                                                                                children: produkt.kod_handlowy\n                                                                            }, produkt.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 736,\n                                                                                columnNumber: 25\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 727,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 745,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Nazwa produktu\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 746,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"nazwa_produktu\",\n                                                                    value: formData.nazwa_produktu,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner\",\n                                                                    placeholder: \"Nazwa produktu...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-purple-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 760,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Nr zam\\xf3wienia, klient, info\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 761,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    name: \"nr_zamowienia\",\n                                                                    value: formData.nr_zamowienia,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 shadow-inner\",\n                                                                    placeholder: \"Nr zam\\xf3wienia...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 763,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: \"⚖️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 779,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent\",\n                                                            children: \"Parametry produkcji\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 781,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 777,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 789,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Waga [g]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 790,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    name: \"waga_input\",\n                                                                    value: formData.waga_input,\n                                                                    onChange: handleChange,\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-all duration-300 shadow-inner text-center\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 787,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 806,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Metraż\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 807,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: metrazEntries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            step: \"0.01\",\n                                                                            value: entry.metraz,\n                                                                            onChange: (e)=>handleMetrazChange(index, \"metraz\", e.target.value),\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner text-center\",\n                                                                            placeholder: \"0.00\"\n                                                                        }, \"metraz-\".concat(index), false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 811,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 809,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 804,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 829,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Ilość [szt]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 830,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 828,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: metrazEntries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            value: entry.ilosc,\n                                                                            onChange: (e)=>handleMetrazChange(index, \"ilosc\", e.target.value),\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner text-center\",\n                                                                            placeholder: \"0\"\n                                                                        }, \"ilosc-\".concat(index), false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 834,\n                                                                            columnNumber: 25\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 832,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-purple-400 rounded-full animate-pulse\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 851,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Suma metr\\xf3w wyprodukowanych\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 852,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 850,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-gradient-to-br from-gray-700 to-gray-600 border border-gray-500 rounded-xl p-4 text-center shadow-lg\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-3xl font-bold\",\n                                                                                    children: totalMeters.toFixed(2)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 856,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xl font-semibold ml-2\",\n                                                                                    children: \"m\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 859,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 855,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full bg-gray-800 rounded-full h-2 mt-2 overflow-hidden\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full animate-pulse\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 862,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 861,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 text-xs text-gray-300 text-center bg-gray-800 bg-opacity-50 p-3 rounded-xl\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold text-gray-200 mb-2\",\n                                                                            children: \"Szczeg\\xf3ły kalkulacji:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 866,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        metrazEntries.map((entry, index)=>{\n                                                                            const metraz = parseFloat(entry.metraz) || 0;\n                                                                            const ilosc = parseFloat(entry.ilosc) || 0;\n                                                                            const total = metraz * ilosc;\n                                                                            return total > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between items-center py-1 border-b border-gray-700 last:border-b-0\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-400\",\n                                                                                        children: [\n                                                                                            \"Pozycja \",\n                                                                                            index + 1,\n                                                                                            \":\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                        lineNumber: 878,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-white font-mono\",\n                                                                                        children: [\n                                                                                            metraz,\n                                                                                            \"m \\xd7 \",\n                                                                                            ilosc,\n                                                                                            \"szt =\",\n                                                                                            \" \",\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                className: \"text-green-400\",\n                                                                                                children: [\n                                                                                                    total.toFixed(2),\n                                                                                                    \"m\"\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                                lineNumber: 883,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                        lineNumber: 881,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, index, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 874,\n                                                                                columnNumber: 27\n                                                                            }, this) : null;\n                                                                        })\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 865,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg mr-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-xl\",\n                                                                children: \"\\uD83E\\uDDF1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 899,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 898,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent\",\n                                                            children: \"SUROWCE\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 901,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 897,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Procent [%]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 907,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 906,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Surowiec\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 912,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 911,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Szarża\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 917,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: [\n                                                                    \"Zużycie [kg]\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 924,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"(waga \\xd7 metry \\xd7 % \\xf7 1000)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 925,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 921,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Opad użytkowy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 931,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 930,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Odpad nieużytkowy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 936,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 17\n                                                }, this),\n                                                surowceEntries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.1\",\n                                                                    value: entry.percentage,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"percentage\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 946,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 945,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: entry.surowiec_id,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"surowiec_id\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Wybierz surowiec\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        surowce.map((surowiec)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: surowiec.id,\n                                                                                children: surowiec.nazwa\n                                                                            }, surowiec.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 977,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 964,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 963,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: entry.szarza,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"szarza\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"Szarża\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 985,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.001\",\n                                                                    value: entry.calculated_usage.toFixed(3),\n                                                                    readOnly: true,\n                                                                    className: \"w-full p-1 bg-gray-600 border border-gray-600 rounded text-white text-xs cursor-not-allowed\",\n                                                                    placeholder: \"0.000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 999,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 998,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.opad_uzytkowy,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"opad_uzytkowy\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1011,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1010,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.odpad_nieuzytkowy,\n                                                                    onChange: (e)=>handleSurowiecChange(index, \"odpad_nieuzytkowy\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1029,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1028,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 943,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 896,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-center mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg mr-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white text-xl\",\n                                                                children: \"\\uD83D\\uDD27\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1052,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1051,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent\",\n                                                            children: \"MATERIAŁY\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1054,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1050,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-12 gap-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-1 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Procent [%]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1060,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1059,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-3 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Materiał\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1064,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Szarża\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1070,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1069,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: [\n                                                                    \"Zużycie\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1077,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"(metry \\xd7 %)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1078,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1075,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1074,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Odpad użytkowy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1082,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"col-span-2 text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-gray-300\",\n                                                                children: \"Odpad nieużytkowy\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1087,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1086,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1058,\n                                                    columnNumber: 17\n                                                }, this),\n                                                materialyEntries.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-12 gap-2 mb-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-1\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.1\",\n                                                                    value: entry.percentage,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"percentage\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1097,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1096,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    value: entry.material_id,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"material_id\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: \"\",\n                                                                            children: \"Wybierz materiał\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1126,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        materialy.map((material)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: material.id,\n                                                                                children: material.nazwa\n                                                                            }, material.id, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                lineNumber: 1128,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1115,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1114,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: entry.status,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"status\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"Szarża\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1137,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1136,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.calculated_usage.toFixed(2),\n                                                                    readOnly: true,\n                                                                    className: \"w-full p-1 bg-gray-600 border border-gray-600 rounded text-white text-xs cursor-not-allowed\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1150,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1149,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.zuzycie,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"zuzycie\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1162,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1161,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"col-span-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"0.01\",\n                                                                    value: entry.odpad,\n                                                                    onChange: (e)=>handleMaterialChange(index, \"odpad\", e.target.value),\n                                                                    className: \"w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500\",\n                                                                    placeholder: \"0.00\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1176,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                        lineNumber: 1094,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1049,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-white font-bold\",\n                                                                children: \"\\uD83D\\uDCDD\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1195,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1194,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-lg font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent\",\n                                                            children: \"Uwagi\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1197,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    name: \"uwagi\",\n                                                    value: formData.uwagi,\n                                                    onChange: handleChange,\n                                                    rows: 4,\n                                                    className: \"w-full p-4 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-all duration-300 shadow-inner resize-none\",\n                                                    placeholder: \"Dodatkowe informacje o produkcji, uwagi, komentarze...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1201,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg mr-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-white text-xl\",\n                                                    children: \"⏱️\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1218,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                lineNumber: 1217,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1216,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-center bg-gradient-to-r from-green-400 to-teal-400 bg-clip-text text-transparent mb-6 pb-3 border-b border-gray-600\",\n                                            children: \"CZAS PRACY\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-800 bg-opacity-30 p-4 rounded-xl border border-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-gray-200 mb-4 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-3 h-3 bg-blue-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1229,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"WYB\\xd3R MASZYNY I PRACOWNIKA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1230,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1228,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1235,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Maszyna\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1236,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-400\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1237,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1234,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"maszyna_id\",\n                                                                            value: formData.maszyna_id,\n                                                                            onChange: handleChange,\n                                                                            required: true,\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Wybierz maszynę...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1246,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                maszyny.map((maszyna)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: maszyna.id,\n                                                                                        children: maszyna.nazwa\n                                                                                    }, maszyna.id, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                        lineNumber: 1248,\n                                                                                        columnNumber: 27\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1239,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1233,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1257,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Pracownik\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1258,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-red-400\",\n                                                                                    children: \"*\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1259,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1256,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                            name: \"pracownik_id\",\n                                                                            value: formData.pracownik_id,\n                                                                            onChange: handleChange,\n                                                                            required: true,\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                    value: \"\",\n                                                                                    children: \"Wybierz pracownika...\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1268,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                pracownicy.map((pracownik)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                        value: pracownik.id,\n                                                                                        children: [\n                                                                                            pracownik.numer,\n                                                                                            \" - \",\n                                                                                            pracownik.imie,\n                                                                                            \" \",\n                                                                                            pracownik.nazwisko\n                                                                                        ]\n                                                                                    }, pracownik.id, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                        lineNumber: 1270,\n                                                                                        columnNumber: 27\n                                                                                    }, this))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1261,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1255,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1232,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1227,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-900 bg-opacity-20 p-4 rounded-xl border border-green-500 border-opacity-30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-green-300 mb-4 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-3 h-3 bg-green-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1283,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CZAS PRACY PRACOWNIKA\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1284,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1282,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1288,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Czas pracy pracownika (min)\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1289,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-red-400\",\n                                                                            children: \"*\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1290,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400 ml-auto\",\n                                                                            children: \"Max: 480 min\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1291,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1287,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"number\",\n                                                                    step: \"1\",\n                                                                    min: \"0\",\n                                                                    max: \"480\",\n                                                                    value: czasPracy.czas_pracy_pracownika,\n                                                                    onChange: (e)=>handleCzasPracyChange(\"czas_pracy_pracownika\", e.target.value),\n                                                                    className: \"w-full p-3 bg-gradient-to-r from-green-800 to-green-700 border border-green-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner\",\n                                                                    placeholder: \"0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1295,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1286,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-yellow-900 bg-opacity-20 p-4 rounded-xl border border-yellow-500 border-opacity-30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-semibold text-yellow-300 mb-4 flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"w-3 h-3 bg-yellow-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1316,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CZASY MASZYNY\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1317,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1315,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"group\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1322,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: \"Czas pracy maszyny (min)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1323,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1321,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"number\",\n                                                                            step: \"1\",\n                                                                            min: \"0\",\n                                                                            value: czasPracy.czas_pracy_maszyny,\n                                                                            onChange: (e)=>handleCzasPracyChange(\"czas_pracy_maszyny\", e.target.value),\n                                                                            className: \"w-full p-3 bg-gradient-to-r from-yellow-800 to-yellow-700 border border-yellow-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-all duration-300 shadow-inner\",\n                                                                            placeholder: \"0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1325,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1320,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"w-2 h-2 bg-orange-400 rounded-full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1344,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Rozgrzanie (min)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1345,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1343,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    step: \"1\",\n                                                                                    min: \"0\",\n                                                                                    value: czasPracy.czas_rozgrzania_maszyny,\n                                                                                    onChange: (e)=>handleCzasPracyChange(\"czas_rozgrzania_maszyny\", e.target.value),\n                                                                                    className: \"w-full p-3 bg-gradient-to-r from-orange-800 to-orange-700 border border-orange-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-orange-400 focus:border-orange-400 transition-all duration-300 shadow-inner\",\n                                                                                    placeholder: \"0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1347,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1342,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"w-2 h-2 bg-purple-400 rounded-full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1365,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Dogrzanie (min)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1366,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1364,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    step: \"1\",\n                                                                                    min: \"0\",\n                                                                                    value: czasPracy.czas_dogrzania_maszyny,\n                                                                                    onChange: (e)=>handleCzasPracyChange(\"czas_dogrzania_maszyny\", e.target.value),\n                                                                                    className: \"w-full p-3 bg-gradient-to-r from-purple-800 to-purple-700 border border-purple-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 shadow-inner\",\n                                                                                    placeholder: \"0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1368,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1363,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1341,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"grid grid-cols-2 gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"w-2 h-2 bg-pink-400 rounded-full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1388,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Przebudowa (min)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1389,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1387,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    step: \"1\",\n                                                                                    min: \"0\",\n                                                                                    value: czasPracy.przebudowa,\n                                                                                    onChange: (e)=>handleCzasPracyChange(\"przebudowa\", e.target.value),\n                                                                                    className: \"w-full p-3 bg-gradient-to-r from-pink-800 to-pink-700 border border-pink-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-pink-400 focus:border-pink-400 transition-all duration-300 shadow-inner\",\n                                                                                    placeholder: \"0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1391,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1386,\n                                                                            columnNumber: 23\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"group\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    className: \"block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"w-2 h-2 bg-indigo-400 rounded-full\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1406,\n                                                                                            columnNumber: 27\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            children: \"Inne (min)\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                            lineNumber: 1407,\n                                                                                            columnNumber: 27\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1405,\n                                                                                    columnNumber: 25\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"number\",\n                                                                                    step: \"1\",\n                                                                                    min: \"0\",\n                                                                                    value: czasPracy.inne,\n                                                                                    onChange: (e)=>handleCzasPracyChange(\"inne\", e.target.value),\n                                                                                    className: \"w-full p-3 bg-gradient-to-r from-indigo-800 to-indigo-700 border border-indigo-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-all duration-300 shadow-inner\",\n                                                                                    placeholder: \"0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                                    lineNumber: 1409,\n                                                                                    columnNumber: 25\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                            lineNumber: 1404,\n                                                                            columnNumber: 23\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                    lineNumber: 1385,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1319,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1314,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"pt-6 border-t border-gray-600 space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            onClick: handleClearForm,\n                                                            className: \"group w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center justify-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"transform group-hover:rotate-12 transition-transform duration-300\",\n                                                                        children: \"\\uD83D\\uDDD1️\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1432,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Wyczyść\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1435,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1431,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1426,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            className: \"group w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-500 hover:to-orange-500 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center justify-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"transform group-hover:scale-110 transition-transform duration-300\",\n                                                                        children: \"\\uD83D\\uDCBE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1444,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Zapamiętaj (1)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1447,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1443,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1439,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"button\",\n                                                            className: \"group w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-500 hover:to-orange-500 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center justify-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"transform group-hover:scale-110 transition-transform duration-300\",\n                                                                        children: \"\\uD83D\\uDCBE\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1456,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Zapamiętaj (2)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1459,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1455,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1451,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            type: \"submit\",\n                                                            form: \"production-form\",\n                                                            disabled: submitting,\n                                                            className: \"group w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 disabled:from-gray-600 disabled:to-gray-700 text-white py-4 px-4 rounded-xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none\",\n                                                            children: submitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center space-x-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1471,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Zapisywanie...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1472,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1470,\n                                                                columnNumber: 23\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"flex items-center justify-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"transform group-hover:scale-110 transition-transform duration-300\",\n                                                                        children: \"✅\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1476,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"ZAPISZ\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                        lineNumber: 1479,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                                lineNumber: 1475,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                            lineNumber: 1463,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                                    lineNumber: 1425,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                            lineNumber: 1225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                                lineNumber: 1214,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\nowy-raport\\\\page.tsx\",\n        lineNumber: 500,\n        columnNumber: 5\n    }, this);\n}\n_s(NowyRaport, \"qSuRAeUZLwaZBC84ic7d1hmlHzw=\");\n_c = NowyRaport;\nvar _c;\n$RefreshReg$(_c, \"NowyRaport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/nowy-raport/page.tsx\n"));

/***/ })

});