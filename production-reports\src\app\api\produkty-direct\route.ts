import { NextResponse } from "next/server";

export async function GET() {
  try {
    const Database = require("better-sqlite3");
    const path = require("path");

    const possiblePaths = [
      path.join(process.cwd(), "baza_danych.db"),
      path.join(process.cwd(), "production-reports", "baza_danych.db"),
      "./baza_danych.db",
      "baza_danych.db",
    ];

    let db;
    for (const dbPath of possiblePaths) {
      try {
        db = new Database(dbPath, { readonly: true });
        break;
      } catch (error) {
        // Continue to next path
      }
    }

    if (!db) {
      return NextResponse.json(
        { error: "Could not connect to database" },
        { status: 500 }
      );
    }

    const produkty = db
      .prepare("SELECT * FROM produkty ORDER BY kod_handlowy ASC")
      .all();

    db.close();

    return NextResponse.json(produkty);
  } catch (error) {
    console.error("Direct database error:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json(
      { error: "Database error: " + errorMessage },
      { status: 500 }
    );
  }
}
