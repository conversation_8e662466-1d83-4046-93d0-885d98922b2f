"use client";

import { useState, useEffect, useMemo } from "react";
import Link from "next/link";

interface Produkt {
  id: number;
  kod_handlowy: string | null;
  nazwa: string | null;
  metraz: number;
  surowiec: string | null;
  material: string | null;
  material_percent: number | null;
  material1: string | null;
  material1_percent: number | null;
  material2: string | null;
  material2_percent: number | null;
  material3: string | null;
  material3_percent: number | null;
  material4: string | null;
  material4_percent: number | null;
  material5: string | null;
  material5_percent: number | null;
  info: string | null;
  wydajnosc: number | null;
}

interface Maszyna {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

interface Pracownik {
  id: number;
  numer: string;
  imie: string | null;
  nazwisko: string | null;
  stanowisko: string | null;
}

interface Surowiec {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

interface Material {
  id: number;
  nazwa: string;
  typ: string | null;
  info: string | null;
}

interface SurowiecEntry {
  percentage: string;
  surowiec_id: string;
  calculated_usage: number;
  szarza: string;
  opad_uzytkowy: string;
  odpad_nieuzytkowy: string;
}

interface MaterialEntry {
  percentage: string;
  material_id: string;
  calculated_usage: number;
  status: string;
  zuzycie: string;
  odpad: string;
}

export default function NowyRaport() {
  const [produkty, setProdukty] = useState<Produkt[]>([]);
  const [maszyny, setMaszyny] = useState<Maszyna[]>([]);
  const [pracownicy, setPracownicy] = useState<Pracownik[]>([]);
  const [surowce, setSurowce] = useState<Surowiec[]>([]);
  const [materialy, setMaterialy] = useState<Material[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  const initialFormData = {
    produkt_id: "",
    kod_handlowy: "",
    nazwa_produktu: "",
    nr_zamowienia: "",
    maszyna_id: "",
    pracownik_id: "",
    uwagi: "",
    waga_input: "",
  };

  const initialMetrazEntries = [
    { metraz: "", ilosc: "" },
    { metraz: "0", ilosc: "" },
    { metraz: "0", ilosc: "" },
    { metraz: "0", ilosc: "" },
  ];

  const initialSurowceEntries: SurowiecEntry[] = Array(5)
    .fill(null)
    .map((_, index) => ({
      percentage: index === 0 ? "100" : "0",
      surowiec_id: "",
      calculated_usage: 0,
      szarza: "",
      opad_uzytkowy: "0.00",
      odpad_nieuzytkowy: "0.00",
    }));

  const initialMaterialyEntries: MaterialEntry[] = Array(5)
    .fill(null)
    .map(() => ({
      percentage: "0",
      material_id: "",
      calculated_usage: 0,
      status: "",
      zuzycie: "0.00",
      odpad: "0.00",
    }));

  const initialCzasPracy = {
    czas_pracy_maszyny: "0",
    czas_pracy_pracownika: "0",
    czas_rozgrzania_maszyny: "0",
    czas_dogrzania_maszyny: "0",
    przebudowa: "0",
    inne: "0",
  };

  const [formData, setFormData] = useState(initialFormData);
  const [metrazEntries, setMetrazEntries] = useState(initialMetrazEntries);
  const [surowceEntries, setSurowceEntries] = useState(initialSurowceEntries);
  const [materialyEntries, setMaterialyEntries] = useState(
    initialMaterialyEntries
  );
  const [czasPracy, setCzasPracy] = useState(initialCzasPracy);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [produktyRes, maszynyRes, pracownicyRes, surowceRes, materialyRes] =
        await Promise.all([
          fetch("/api/produkty-direct"), // Use direct endpoint
          fetch("/api/maszyny"),
          fetch("/api/pracownicy"),
          fetch("/api/surowce"),
          fetch("/api/materialy"),
        ]);

      const produktyData = await produktyRes.json();
      setProdukty(produktyData);
      setMaszyny(await maszynyRes.json());
      setPracownicy(await pracownicyRes.json());
      setSurowce(await surowceRes.json());
      setMaterialy(await materialyRes.json());
    } catch (error) {
      console.error("Błąd podczas ładowania danych:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const reportData = {
        produkt_id: parseInt(formData.produkt_id),
        metraz_rolek: calculateTotalMeters(),
        ilosc_rolek: metrazEntries.reduce(
          (sum, entry) => sum + (parseInt(entry.ilosc) || 0),
          0
        ),
        surowce: surowceEntries
          .filter((s) => s.surowiec_id)
          .map((s) => ({
            surowiec_id: parseInt(s.surowiec_id),
            zuzyty_surowiec: parseFloat(s.opad_uzytkowy),
            odpad_surowiec: parseFloat(s.odpad_nieuzytkowy),
            szarza: s.szarza,
          })),
        materialy: materialyEntries
          .filter((m) => m.material_id)
          .map((m) => ({
            material_id: parseInt(m.material_id),
            zuzyty_material: parseFloat(m.zuzycie),
            odpad_material: parseFloat(m.odpad),
            szarza: m.status,
          })),
        maszyna_id: parseInt(formData.maszyna_id),
        pracownik_id: parseInt(formData.pracownik_id),
        czas_pracy_maszyny: parseFloat(czasPracy.czas_pracy_maszyny),
        czas_pracy_pracownika: parseFloat(czasPracy.czas_pracy_pracownika),
        uwagi: formData.uwagi,
      };

      const response = await fetch("/api/raporty-direct", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(reportData),
      });

      if (response.ok) {
        alert("Raport został dodany pomyślnie!");
        handleClearForm();
      } else {
        const errorData = await response.json();
        alert(`Błąd podczas dodawania raportu: ${errorData.error}`);
      }
    } catch (error) {
      console.error("Błąd:", error);
      alert("Wystąpił błąd podczas wysyłania formularza.");
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    const weight = parseFloat(formData.waga_input) || 0;
    const totalMeters = calculateTotalMeters();

    const newSurowceEntries = surowceEntries.map((entry) => ({
      ...entry,
      calculated_usage: calculateUsage(
        weight,
        parseFloat(entry.percentage) || 0,
        totalMeters
      ),
    }));
    setSurowceEntries(newSurowceEntries);

    const newMaterialEntries = materialyEntries.map((entry) => ({
      ...entry,
      calculated_usage: calculateMaterialUsage(
        parseFloat(entry.percentage) || 0,
        totalMeters
      ),
    }));
    setMaterialyEntries(newMaterialEntries);
  }, [formData.waga_input, metrazEntries]);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    if (name === "produkt_id" && value) {
      const selectedProduct = produkty.find((p) => p.id === parseInt(value));
      if (selectedProduct) {
        setFormData((prev) => ({
          ...prev,
          nazwa_produktu: selectedProduct.nazwa || "",
        }));

        const newMetrazEntries = [...metrazEntries];
        newMetrazEntries[0] = {
          ...newMetrazEntries[0],
          metraz: selectedProduct.metraz.toString(),
        };
        setMetrazEntries(newMetrazEntries);

        if (selectedProduct.surowiec) {
          const matchingSurowiec = surowce.find(
            (s) => s.nazwa.trim() === selectedProduct.surowiec!.trim()
          );
          if (matchingSurowiec) {
            const newEntries = [...surowceEntries];
            newEntries[0] = {
              ...newEntries[0],
              surowiec_id: matchingSurowiec.id.toString(),
            };
            setSurowceEntries(newEntries);
          }
        }

        const totalMeters = newMetrazEntries.reduce((sum, entry) => {
          const metraz = parseFloat(entry.metraz) || 0;
          const ilosc = parseFloat(entry.ilosc) || 0;
          return sum + metraz * ilosc;
        }, 0);

        const newMaterialEntries = [...initialMaterialyEntries];

        const materialFields = [
          {
            name: selectedProduct.material,
            percent: selectedProduct.material_percent,
          },
          {
            name: selectedProduct.material1,
            percent: selectedProduct.material1_percent,
          },
          {
            name: selectedProduct.material2,
            percent: selectedProduct.material2_percent,
          },
          {
            name: selectedProduct.material3,
            percent: selectedProduct.material3_percent,
          },
          {
            name: selectedProduct.material4,
            percent: selectedProduct.material4_percent,
          },
          {
            name: selectedProduct.material5,
            percent: selectedProduct.material5_percent,
          },
        ];

        let entryIndex = 0;
        materialFields.forEach((materialField) => {
          if (materialField.name && entryIndex < newMaterialEntries.length) {
            const matchingMaterial = materialy.find(
              (m) => m.nazwa.trim() === materialField.name!.trim()
            );
            if (matchingMaterial) {
              const materialPercentage = materialField.percent || 0;
              newMaterialEntries[entryIndex] = {
                ...newMaterialEntries[entryIndex],
                material_id: matchingMaterial.id.toString(),
                percentage: materialPercentage.toString(),
                calculated_usage: calculateMaterialUsage(
                  materialPercentage,
                  totalMeters
                ),
              };
              entryIndex++;
            }
          }
        });

        setMaterialyEntries(newMaterialEntries);
      }
    }
  };

  // Calculate usage based on weight, percentage, and total meters (result in kg)
  const calculateUsage = (
    weight: number,
    percentage: number,
    totalMeters: number
  ): number => {
    // Convert from grams to kilograms
    return (weight * (percentage / 100) * totalMeters) / 1000;
  };

  // Calculate material usage based on total meters and percentage (no weight)
  const calculateMaterialUsage = (
    percentage: number,
    totalMeters: number
  ): number => {
    return totalMeters * (percentage / 100);
  };

  // Calculate total meters produced
  const calculateTotalMeters = (): number => {
    return metrazEntries.reduce((sum, entry) => {
      const metraz = parseFloat(entry.metraz) || 0;
      const ilosc = parseFloat(entry.ilosc) || 0;
      return sum + metraz * ilosc;
    }, 0);
  };

  // Calculate efficiency percentage based on wydajnosc (per 8h shift), meters produced, and time used
  const calculateEfficiencyPercentage = (): number => {
    const selectedProduct = produkty.find(
      (p) => p.id === parseInt(formData.produkt_id)
    );

    if (!selectedProduct || !selectedProduct.wydajnosc) {
      return 0;
    }

    const totalMeters = calculateTotalMeters();
    const timeUsedMinutes = parseFloat(czasPracy.czas_pracy_maszyny) || 0;
    const timeUsedHours = timeUsedMinutes / 60; // Convert minutes to hours

    if (timeUsedMinutes === 0) return 0;

    // wydajnosc is per 8-hour shift, so calculate expected production for the time used
    const expectedMetersForTimeUsed =
      (selectedProduct.wydajnosc * timeUsedHours) / 8;

    // Calculate efficiency percentage: actual vs expected for the time period
    const efficiency = (totalMeters / expectedMetersForTimeUsed) * 100;

    return Math.min(efficiency, 999); // Allow over 100% efficiency
  };

  const handleSurowiecChange = (
    index: number,
    field: keyof SurowiecEntry,
    value: string
  ) => {
    const newEntries = [...surowceEntries];
    (newEntries[index] as any)[field] = value;
    setSurowceEntries(newEntries);
  };

  const handleMetrazChange = (
    index: number,
    field: "metraz" | "ilosc",
    value: string
  ) => {
    const newEntries = [...metrazEntries];
    newEntries[index] = { ...newEntries[index], [field]: value };
    setMetrazEntries(newEntries);
  };

  const handleMaterialChange = (
    index: number,
    field: keyof MaterialEntry,
    value: string
  ) => {
    const newEntries = [...materialyEntries];
    (newEntries[index] as any)[field] = value;
    setMaterialyEntries(newEntries);
  };

  const handleCzasPracyChange = (field: string, value: string) => {
    const updatedCzasPracy = { ...czasPracy, [field]: value };
    const numValue = parseFloat(value) || 0;

    if (field === "czas_pracy_pracownika" && numValue > 480) {
      alert("Czas pracy pracownika nie może przekroczyć 480 minut (8 godzin)");
      return;
    }

    const {
      czas_pracy_pracownika,
      czas_pracy_maszyny,
      czas_rozgrzania_maszyny,
      czas_dogrzania_maszyny,
      przebudowa,
      inne,
    } = updatedCzasPracy;

    const machineTimeSum =
      parseFloat(czas_pracy_maszyny) +
      parseFloat(czas_rozgrzania_maszyny) +
      parseFloat(czas_dogrzania_maszyny) +
      parseFloat(przebudowa) +
      parseFloat(inne);

    const workerTime = parseFloat(czas_pracy_pracownika);

    if (machineTimeSum > workerTime && workerTime > 0) {
      alert(
        `Suma czasów maszyny (${machineTimeSum} min) nie może przekroczyć czasu pracy pracownika (${workerTime} min)`
      );
      return;
    }

    setCzasPracy(updatedCzasPracy);
  };

  const handleClearForm = () => {
    setFormData(initialFormData);
    setSurowceEntries(initialSurowceEntries);
    setMetrazEntries(initialMetrazEntries);
    setMaterialyEntries(initialMaterialyEntries);
    setCzasPracy(initialCzasPracy);
  };

  const totalMeters = useMemo(() => calculateTotalMeters(), [metrazEntries]);
  const efficiencyPercentage = useMemo(
    () => calculateEfficiencyPercentage(),
    [produkty, formData.produkt_id, totalMeters, czasPracy.czas_pracy_maszyny]
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="bg-gray-800 rounded-xl p-8 border border-gray-700 shadow-xl">
          <div className="flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <div className="text-xl text-white">Ładowanie danych...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-indigo-500 rounded-full mix-blend-multiply filter blur-xl opacity-10 animate-pulse animation-delay-4000"></div>
      </div>

      <div className="max-w-[1600px] mx-auto relative z-10">
        {/* Header */}
        <div className="bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 mb-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-lg">📊</span>
                </div>
                <div>
                  <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    06-A-1
                  </h1>
                  <p className="text-gray-400 text-sm">
                    Production Report System
                  </p>
                </div>
              </div>
              <div className="flex space-x-3">
                <Link
                  href="/"
                  className="group bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-500 hover:to-gray-600 text-white px-6 py-3 rounded-xl transition-all duration-300 border border-gray-500 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <span className="flex items-center space-x-2">
                    <span className="transform group-hover:-translate-x-1 transition-transform duration-300">
                      ←
                    </span>
                    <span>Powrót</span>
                  </span>
                </Link>
                <Link
                  href="/admin-db"
                  className="group bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-500 hover:to-indigo-500 text-white px-6 py-3 rounded-xl transition-all duration-300 border border-purple-400 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <span className="flex items-center space-x-2">
                    <span className="transform group-hover:rotate-12 transition-transform duration-300">
                      🗄️
                    </span>
                    <span>Zarządzaj bazą danych</span>
                  </span>
                </Link>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 bg-green-900 bg-opacity-50 px-4 py-2 rounded-full border border-green-500">
                <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
                <span className="text-green-300 font-semibold text-sm">
                  System Online
                </span>
              </div>
              <div className="text-gray-400 text-sm">
                {new Date().toLocaleTimeString("pl-PL")}
              </div>
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="grid grid-cols-12 gap-4">
          {/* Left Sidebar */}
          <div className="col-span-2">
            <div className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
              <div className="flex items-center justify-center mb-6">
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-3 shadow-lg">
                  <span className="text-white text-xl">ℹ️</span>
                </div>
              </div>
              <h3 className="text-xl font-bold text-center bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-6 pb-3 border-b border-gray-600">
                INFORMACJE
              </h3>

              <div className="space-y-4">
                <div className="group">
                  <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                    <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                    <span>Odbiorca</span>
                  </label>
                  <input
                    type="text"
                    className="w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner"
                    placeholder="Wprowadź odbiorcę..."
                  />
                </div>

                <div className="group">
                  <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                    <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                    <span>Wydajność [m/8h]</span>
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={
                        produkty.find(
                          (p) => p.id === parseInt(formData.produkt_id)
                        )?.wydajnosc || ""
                      }
                      readOnly
                      className="w-full p-3 bg-gradient-to-r from-gray-600 to-gray-500 border border-gray-500 rounded-xl text-white text-sm cursor-not-allowed shadow-inner"
                      placeholder="Automatycznie wypełniane..."
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                      <span className="text-gray-400 text-xs">🔒</span>
                    </div>
                  </div>
                </div>

                {formData.produkt_id && (
                  <div className="mt-6 p-4 bg-gradient-to-br from-gray-700 to-gray-600 rounded-xl border border-gray-500 shadow-lg">
                    <div className="flex justify-between items-center mb-3">
                      <label className="flex items-center space-x-2 text-sm font-medium text-gray-200">
                        <span className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></span>
                        <span>Efektywność</span>
                      </label>
                      <div className="bg-gray-800 px-3 py-1 rounded-full border border-gray-600">
                        <span className="text-lg font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                          {efficiencyPercentage.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    <div className="relative w-full bg-gray-800 rounded-full h-4 border border-gray-600 shadow-inner overflow-hidden">
                      <div
                        className={`h-4 rounded-full transition-all duration-1000 ease-out relative overflow-hidden ${
                          efficiencyPercentage >= 80
                            ? "bg-gradient-to-r from-green-400 to-green-500"
                            : efficiencyPercentage >= 60
                            ? "bg-gradient-to-r from-yellow-400 to-orange-400"
                            : "bg-gradient-to-r from-red-400 to-red-500"
                        }`}
                        style={{
                          width: `${Math.min(efficiencyPercentage, 100)}%`,
                        }}
                      >
                        <div className="absolute inset-0 bg-white opacity-20 animate-pulse"></div>
                      </div>
                    </div>
                    <div className="mt-3 text-xs text-gray-300 text-center bg-gray-800 bg-opacity-50 p-2 rounded-lg">
                      <div className="font-semibold">
                        {totalMeters.toFixed(1)}m ÷{" "}
                        {(() => {
                          const selectedProduct = produkty.find(
                            (p) => p.id === parseInt(formData.produkt_id)
                          );
                          const timeUsedMinutes =
                            parseFloat(czasPracy.czas_pracy_maszyny) || 0;
                          const timeUsedHours = timeUsedMinutes / 60;
                          const expectedForTime = selectedProduct?.wydajnosc
                            ? (
                                (selectedProduct.wydajnosc * timeUsedHours) /
                                8
                              ).toFixed(1)
                            : "0.0";
                          return `${expectedForTime}m`;
                        })()}
                      </div>
                      <div className="text-gray-400 mt-1">
                        Oczekiwane za{" "}
                        {parseFloat(czasPracy.czas_pracy_maszyny) || 0} min
                      </div>
                    </div>
                  </div>
                )}

                <div className="pt-4 border-t border-gray-600">
                  <div className="space-y-2 text-sm text-gray-300">
                    <div className="flex justify-between">
                      <span>Kartoteka:</span>
                      <span className="text-white">-</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Giza:</span>
                      <span className="text-white">-</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Info2:</span>
                      <span className="text-white">-</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Info3:</span>
                      <span className="text-white">-</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Info4:</span>
                      <span className="text-white">-</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Info5:</span>
                      <span className="text-white">-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Form Area */}
          <div className="col-span-8">
            <form
              id="production-form"
              onSubmit={handleSubmit}
              className="space-y-4"
            >
              {/* Product Header */}
              <div className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg mr-3">
                    <span className="text-white font-bold">📦</span>
                  </div>
                  <h3 className="text-xl font-bold bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                    Informacje o produkcie
                  </h3>
                </div>
                <div className="grid grid-cols-12 gap-6">
                  <div className="col-span-3">
                    <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                      <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                      <span>Kod handlowy</span>
                      <span className="text-red-400">*</span>
                    </label>
                    <select
                      name="produkt_id"
                      value={formData.produkt_id}
                      onChange={handleChange}
                      required
                      className="w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner"
                    >
                      <option value="">Wybierz produkt...</option>
                      {produkty.map((produkt) => (
                        <option key={produkt.id} value={produkt.id}>
                          {produkt.kod_handlowy}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="col-span-6">
                    <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                      <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                      <span>Nazwa produktu</span>
                    </label>
                    <input
                      type="text"
                      name="nazwa_produktu"
                      value={formData.nazwa_produktu}
                      onChange={handleChange}
                      className="w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner"
                      placeholder="Nazwa produktu..."
                    />
                  </div>

                  <div className="col-span-3">
                    <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                      <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                      <span>Nr zamówienia, klient, info</span>
                    </label>
                    <input
                      type="text"
                      name="nr_zamowienia"
                      value={formData.nr_zamowienia}
                      onChange={handleChange}
                      className="w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 shadow-inner"
                      placeholder="Nr zamówienia..."
                    />
                  </div>
                </div>
              </div>

              {/* Weight, Metraz and Quantity Inputs */}
              <div className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-yellow-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg mr-3">
                    <span className="text-white font-bold">⚖️</span>
                  </div>
                  <h3 className="text-xl font-bold bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                    Parametry produkcji
                  </h3>
                </div>
                <div className="grid grid-cols-12 gap-6">
                  {/* Weight */}
                  <div className="col-span-2">
                    <label className="block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2">
                      <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
                      <span>Waga [g]</span>
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      name="waga_input"
                      value={formData.waga_input}
                      onChange={handleChange}
                      className="w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-all duration-300 shadow-inner text-center"
                      placeholder="0.00"
                    />
                  </div>

                  {/* Metraz Column */}
                  <div className="col-span-3">
                    <label className="block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2">
                      <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                      <span>Metraż</span>
                    </label>
                    <div className="space-y-2">
                      {metrazEntries.map((entry, index) => (
                        <input
                          key={`metraz-${index}`}
                          type="number"
                          step="0.01"
                          value={entry.metraz}
                          onChange={(e) =>
                            handleMetrazChange(index, "metraz", e.target.value)
                          }
                          className="w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner text-center"
                          placeholder="0.00"
                        />
                      ))}
                    </div>
                  </div>

                  {/* Quantity Column */}
                  <div className="col-span-3">
                    <label className="block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2">
                      <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                      <span>Ilość [szt]</span>
                    </label>
                    <div className="space-y-2">
                      {metrazEntries.map((entry, index) => (
                        <input
                          key={`ilosc-${index}`}
                          type="number"
                          value={entry.ilosc}
                          onChange={(e) =>
                            handleMetrazChange(index, "ilosc", e.target.value)
                          }
                          className="w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner text-center"
                          placeholder="0"
                        />
                      ))}
                    </div>
                  </div>

                  {/* Total Meters Display */}
                  <div className="col-span-4">
                    <label className="block text-sm font-medium text-gray-300 mb-2 text-center flex items-center justify-center space-x-2">
                      <span className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></span>
                      <span>Suma metrów wyprodukowanych</span>
                    </label>
                    <div className="bg-gradient-to-br from-gray-700 to-gray-600 border border-gray-500 rounded-xl p-4 text-center shadow-lg">
                      <div className="bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent">
                        <span className="text-3xl font-bold">
                          {totalMeters.toFixed(2)}
                        </span>
                        <span className="text-xl font-semibold ml-2">m</span>
                      </div>
                      <div className="w-full bg-gray-800 rounded-full h-2 mt-2 overflow-hidden">
                        <div className="h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full animate-pulse"></div>
                      </div>
                    </div>
                    <div className="mt-3 text-xs text-gray-300 text-center bg-gray-800 bg-opacity-50 p-3 rounded-xl">
                      <div className="font-semibold text-gray-200 mb-2">
                        Szczegóły kalkulacji:
                      </div>
                      {metrazEntries.map((entry, index) => {
                        const metraz = parseFloat(entry.metraz) || 0;
                        const ilosc = parseFloat(entry.ilosc) || 0;
                        const total = metraz * ilosc;
                        return total > 0 ? (
                          <div
                            key={index}
                            className="flex justify-between items-center py-1 border-b border-gray-700 last:border-b-0"
                          >
                            <span className="text-gray-400">
                              Pozycja {index + 1}:
                            </span>
                            <span className="text-white font-mono">
                              {metraz}m × {ilosc}szt ={" "}
                              <span className="text-green-400">
                                {total.toFixed(2)}m
                              </span>
                            </span>
                          </div>
                        ) : null;
                      })}
                    </div>
                  </div>
                </div>
              </div>

              {/* Raw Materials Section */}
              <div className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
                <div className="flex items-center justify-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg mr-4">
                    <span className="text-white text-xl">🧱</span>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text text-transparent">
                    SUROWCE
                  </h3>
                </div>
                <div className="grid grid-cols-12 gap-2 mb-2">
                  <div className="col-span-1 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Procent [%]
                    </span>
                  </div>
                  <div className="col-span-3 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Surowiec
                    </span>
                  </div>
                  <div className="col-span-2 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Szarża
                    </span>
                  </div>
                  <div className="col-span-2 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Zużycie [kg]
                      <br />
                      <span className="text-xs text-gray-400">
                        (waga × metry × % ÷ 1000)
                      </span>
                    </span>
                  </div>
                  <div className="col-span-2 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Opad użytkowy
                    </span>
                  </div>
                  <div className="col-span-2 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Odpad nieużytkowy
                    </span>
                  </div>
                </div>

                {surowceEntries.map((entry, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 mb-2">
                    {/* Percentage */}
                    <div className="col-span-1">
                      <input
                        type="number"
                        step="0.1"
                        value={entry.percentage}
                        onChange={(e) =>
                          handleSurowiecChange(
                            index,
                            "percentage",
                            e.target.value
                          )
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                        placeholder="0"
                      />
                    </div>

                    {/* Surowiec Selection */}
                    <div className="col-span-3">
                      <select
                        value={entry.surowiec_id}
                        onChange={(e) =>
                          handleSurowiecChange(
                            index,
                            "surowiec_id",
                            e.target.value
                          )
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="">Wybierz surowiec</option>
                        {surowce.map((surowiec) => (
                          <option key={surowiec.id} value={surowiec.id}>
                            {surowiec.nazwa}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Szarża */}
                    <div className="col-span-2">
                      <input
                        type="text"
                        value={entry.szarza}
                        onChange={(e) =>
                          handleSurowiecChange(index, "szarza", e.target.value)
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                        placeholder="Szarża"
                      />
                    </div>

                    {/* Calculated Usage Display */}
                    <div className="col-span-2">
                      <input
                        type="number"
                        step="0.001"
                        value={entry.calculated_usage.toFixed(3)}
                        readOnly
                        className="w-full p-1 bg-gray-600 border border-gray-600 rounded text-white text-xs cursor-not-allowed"
                        placeholder="0.000"
                      />
                    </div>

                    {/* Opad użytkowy */}
                    <div className="col-span-2">
                      <input
                        type="number"
                        step="0.01"
                        value={entry.opad_uzytkowy}
                        onChange={(e) =>
                          handleSurowiecChange(
                            index,
                            "opad_uzytkowy",
                            e.target.value
                          )
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                        placeholder="0.00"
                      />
                    </div>

                    {/* Odpad nieużytkowy */}
                    <div className="col-span-2">
                      <input
                        type="number"
                        step="0.01"
                        value={entry.odpad_nieuzytkowy}
                        onChange={(e) =>
                          handleSurowiecChange(
                            index,
                            "odpad_nieuzytkowy",
                            e.target.value
                          )
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                ))}
              </div>

              {/* Materials Section */}
              <div className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
                <div className="flex items-center justify-center mb-6">
                  <div className="w-12 h-12 bg-gradient-to-br from-cyan-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg mr-4">
                    <span className="text-white text-xl">🔧</span>
                  </div>
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                    MATERIAŁY
                  </h3>
                </div>
                <div className="grid grid-cols-12 gap-2 mb-2">
                  <div className="col-span-1 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Procent [%]
                    </span>
                  </div>
                  <div className="col-span-3 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Materiał
                    </span>
                  </div>
                  <div className="col-span-2 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Szarża
                    </span>
                  </div>
                  <div className="col-span-2 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Zużycie
                      <br />
                      <span className="text-xs text-gray-400">(metry × %)</span>
                    </span>
                  </div>
                  <div className="col-span-2 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Odpad użytkowy
                    </span>
                  </div>
                  <div className="col-span-2 text-center">
                    <span className="text-sm font-medium text-gray-300">
                      Odpad nieużytkowy
                    </span>
                  </div>
                </div>

                {materialyEntries.map((entry, index) => (
                  <div key={index} className="grid grid-cols-12 gap-2 mb-2">
                    {/* Percentage */}
                    <div className="col-span-1">
                      <input
                        type="number"
                        step="0.1"
                        value={entry.percentage}
                        onChange={(e) =>
                          handleMaterialChange(
                            index,
                            "percentage",
                            e.target.value
                          )
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                        placeholder="0"
                      />
                    </div>

                    {/* Material Selection */}
                    <div className="col-span-3">
                      <select
                        value={entry.material_id}
                        onChange={(e) =>
                          handleMaterialChange(
                            index,
                            "material_id",
                            e.target.value
                          )
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                      >
                        <option value="">Wybierz materiał</option>
                        {materialy.map((material) => (
                          <option key={material.id} value={material.id}>
                            {material.nazwa}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Szarża */}
                    <div className="col-span-2">
                      <input
                        type="text"
                        value={entry.status}
                        onChange={(e) =>
                          handleMaterialChange(index, "status", e.target.value)
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                        placeholder="Szarża"
                      />
                    </div>

                    {/* Calculated Usage Display */}
                    <div className="col-span-2">
                      <input
                        type="number"
                        step="0.01"
                        value={entry.calculated_usage.toFixed(2)}
                        readOnly
                        className="w-full p-1 bg-gray-600 border border-gray-600 rounded text-white text-xs cursor-not-allowed"
                        placeholder="0.00"
                      />
                    </div>

                    {/* Odpad użytkowy */}
                    <div className="col-span-2">
                      <input
                        type="number"
                        step="0.01"
                        value={entry.zuzycie}
                        onChange={(e) =>
                          handleMaterialChange(index, "zuzycie", e.target.value)
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                        placeholder="0.00"
                      />
                    </div>

                    {/* Odpad nieużytkowy */}
                    <div className="col-span-2">
                      <input
                        type="number"
                        step="0.01"
                        value={entry.odpad}
                        onChange={(e) =>
                          handleMaterialChange(index, "odpad", e.target.value)
                        }
                        className="w-full p-1 bg-gray-700 border border-gray-600 rounded text-white text-xs focus:ring-1 focus:ring-blue-500"
                        placeholder="0.00"
                      />
                    </div>
                  </div>
                ))}
              </div>

              {/* Notes Section */}
              <div className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg mr-3">
                    <span className="text-white font-bold">📝</span>
                  </div>
                  <label className="text-lg font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                    Uwagi
                  </label>
                </div>
                <textarea
                  name="uwagi"
                  value={formData.uwagi}
                  onChange={handleChange}
                  rows={4}
                  className="w-full p-4 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-all duration-300 shadow-inner resize-none"
                  placeholder="Dodatkowe informacje o produkcji, uwagi, komentarze..."
                />
              </div>
            </form>
          </div>

          {/* Right Sidebar */}
          <div className="col-span-2">
            <div className="bg-gradient-to-br from-gray-800 via-gray-700 to-gray-800 rounded-2xl border border-gray-600 p-6 shadow-2xl backdrop-blur-sm bg-opacity-90">
              <div className="flex items-center justify-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg mr-4">
                  <span className="text-white text-xl">⏱️</span>
                </div>
              </div>
              <h3 className="text-xl font-bold text-center bg-gradient-to-r from-green-400 to-teal-400 bg-clip-text text-transparent mb-6 pb-3 border-b border-gray-600">
                CZAS PRACY
              </h3>

              <div className="space-y-6">
                {/* Machine and Worker Selection */}
                <div className="bg-gray-800 bg-opacity-30 p-4 rounded-xl border border-gray-600">
                  <h4 className="text-sm font-semibold text-gray-200 mb-4 flex items-center space-x-2">
                    <span className="w-3 h-3 bg-blue-400 rounded-full"></span>
                    <span>WYBÓR MASZYNY I PRACOWNIKA</span>
                  </h4>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="group">
                      <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                        <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                        <span>Maszyna</span>
                        <span className="text-red-400">*</span>
                      </label>
                      <select
                        name="maszyna_id"
                        value={formData.maszyna_id}
                        onChange={handleChange}
                        required
                        className="w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-blue-400 focus:border-blue-400 transition-all duration-300 shadow-inner"
                      >
                        <option value="">Wybierz maszynę...</option>
                        {maszyny.map((maszyna) => (
                          <option key={maszyna.id} value={maszyna.id}>
                            {maszyna.nazwa}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="group">
                      <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                        <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                        <span>Pracownik</span>
                        <span className="text-red-400">*</span>
                      </label>
                      <select
                        name="pracownik_id"
                        value={formData.pracownik_id}
                        onChange={handleChange}
                        required
                        className="w-full p-3 bg-gradient-to-r from-gray-700 to-gray-600 border border-gray-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner"
                      >
                        <option value="">Wybierz pracownika...</option>
                        {pracownicy.map((pracownik) => (
                          <option key={pracownik.id} value={pracownik.id}>
                            {pracownik.numer} - {pracownik.imie}{" "}
                            {pracownik.nazwisko}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>

                {/* Worker Time Section */}
                <div className="bg-green-900 bg-opacity-20 p-4 rounded-xl border border-green-500 border-opacity-30">
                  <h4 className="text-sm font-semibold text-green-300 mb-4 flex items-center space-x-2">
                    <span className="w-3 h-3 bg-green-400 rounded-full"></span>
                    <span>CZAS PRACY PRACOWNIKA</span>
                  </h4>
                  <div className="group">
                    <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                      <span className="w-2 h-2 bg-green-400 rounded-full"></span>
                      <span>Czas pracy pracownika (min)</span>
                      <span className="text-red-400">*</span>
                      <span className="text-xs text-gray-400 ml-auto">
                        Max: 480 min
                      </span>
                    </label>
                    <input
                      type="number"
                      step="1"
                      min="0"
                      max="480"
                      value={czasPracy.czas_pracy_pracownika}
                      onChange={(e) =>
                        handleCzasPracyChange(
                          "czas_pracy_pracownika",
                          e.target.value
                        )
                      }
                      className="w-full p-3 bg-gradient-to-r from-green-800 to-green-700 border border-green-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-green-400 focus:border-green-400 transition-all duration-300 shadow-inner"
                      placeholder="0"
                    />
                  </div>
                </div>

                {/* Machine Times Section */}
                <div className="bg-yellow-900 bg-opacity-20 p-4 rounded-xl border border-yellow-500 border-opacity-30">
                  <h4 className="text-sm font-semibold text-yellow-300 mb-4 flex items-center space-x-2">
                    <span className="w-3 h-3 bg-yellow-400 rounded-full"></span>
                    <span>CZASY MASZYNY</span>
                  </h4>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="group">
                      <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                        <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
                        <span>Czas pracy maszyny (min)</span>
                      </label>
                      <input
                        type="number"
                        step="1"
                        min="0"
                        value={czasPracy.czas_pracy_maszyny}
                        onChange={(e) =>
                          handleCzasPracyChange(
                            "czas_pracy_maszyny",
                            e.target.value
                          )
                        }
                        className="w-full p-3 bg-gradient-to-r from-yellow-800 to-yellow-700 border border-yellow-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 transition-all duration-300 shadow-inner"
                        placeholder="0"
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="group">
                        <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                          <span className="w-2 h-2 bg-orange-400 rounded-full"></span>
                          <span>Rozgrzanie (min)</span>
                        </label>
                        <input
                          type="number"
                          step="1"
                          min="0"
                          value={czasPracy.czas_rozgrzania_maszyny}
                          onChange={(e) =>
                            handleCzasPracyChange(
                              "czas_rozgrzania_maszyny",
                              e.target.value
                            )
                          }
                          className="w-full p-3 bg-gradient-to-r from-orange-800 to-orange-700 border border-orange-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-orange-400 focus:border-orange-400 transition-all duration-300 shadow-inner"
                          placeholder="0"
                        />
                      </div>

                      <div className="group">
                        <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                          <span className="w-2 h-2 bg-purple-400 rounded-full"></span>
                          <span>Dogrzanie (min)</span>
                        </label>
                        <input
                          type="number"
                          step="1"
                          min="0"
                          value={czasPracy.czas_dogrzania_maszyny}
                          onChange={(e) =>
                            handleCzasPracyChange(
                              "czas_dogrzania_maszyny",
                              e.target.value
                            )
                          }
                          className="w-full p-3 bg-gradient-to-r from-purple-800 to-purple-700 border border-purple-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-purple-400 focus:border-purple-400 transition-all duration-300 shadow-inner"
                          placeholder="0"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="group">
                        <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                          <span className="w-2 h-2 bg-pink-400 rounded-full"></span>
                          <span>Przebudowa (min)</span>
                        </label>
                        <input
                          type="number"
                          step="1"
                          min="0"
                          value={czasPracy.przebudowa}
                          onChange={(e) =>
                            handleCzasPracyChange("przebudowa", e.target.value)
                          }
                          className="w-full p-3 bg-gradient-to-r from-pink-800 to-pink-700 border border-pink-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-pink-400 focus:border-pink-400 transition-all duration-300 shadow-inner"
                          placeholder="0"
                        />
                      </div>

                      <div className="group">
                        <label className="block text-sm font-medium text-gray-300 mb-2 flex items-center space-x-2">
                          <span className="w-2 h-2 bg-indigo-400 rounded-full"></span>
                          <span>Inne (min)</span>
                        </label>
                        <input
                          type="number"
                          step="1"
                          min="0"
                          value={czasPracy.inne}
                          onChange={(e) =>
                            handleCzasPracyChange("inne", e.target.value)
                          }
                          className="w-full p-3 bg-gradient-to-r from-indigo-800 to-indigo-700 border border-indigo-500 rounded-xl text-white text-sm focus:ring-2 focus:ring-indigo-400 focus:border-indigo-400 transition-all duration-300 shadow-inner"
                          placeholder="0"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="pt-6 border-t border-gray-600 space-y-3">
                  <button
                    type="button"
                    onClick={handleClearForm}
                    className="group w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    <span className="flex items-center justify-center space-x-2">
                      <span className="transform group-hover:rotate-12 transition-transform duration-300">
                        🗑️
                      </span>
                      <span>Wyczyść</span>
                    </span>
                  </button>

                  <button
                    type="button"
                    className="group w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-500 hover:to-orange-500 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    <span className="flex items-center justify-center space-x-2">
                      <span className="transform group-hover:scale-110 transition-transform duration-300">
                        💾
                      </span>
                      <span>Zapamiętaj (1)</span>
                    </span>
                  </button>

                  <button
                    type="button"
                    className="group w-full bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-500 hover:to-orange-500 text-white py-3 px-4 rounded-xl text-sm font-semibold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    <span className="flex items-center justify-center space-x-2">
                      <span className="transform group-hover:scale-110 transition-transform duration-300">
                        💾
                      </span>
                      <span>Zapamiętaj (2)</span>
                    </span>
                  </button>

                  <button
                    type="submit"
                    form="production-form"
                    disabled={submitting}
                    className="group w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-500 hover:to-emerald-500 disabled:from-gray-600 disabled:to-gray-700 text-white py-4 px-4 rounded-xl font-bold text-lg transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none"
                  >
                    {submitting ? (
                      <div className="flex items-center justify-center space-x-3">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Zapisywanie...</span>
                      </div>
                    ) : (
                      <span className="flex items-center justify-center space-x-2">
                        <span className="transform group-hover:scale-110 transition-transform duration-300">
                          ✅
                        </span>
                        <span>ZAPISZ</span>
                      </span>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
