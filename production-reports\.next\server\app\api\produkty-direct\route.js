/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/produkty-direct/route";
exports.ids = ["app/api/produkty-direct/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprodukty-direct%2Froute&page=%2Fapi%2Fprodukty-direct%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprodukty-direct%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprodukty-direct%2Froute&page=%2Fapi%2Fprodukty-direct%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprodukty-direct%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_produkty_direct_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/produkty-direct/route.ts */ \"(rsc)/./src/app/api/produkty-direct/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/produkty-direct/route\",\n        pathname: \"/api/produkty-direct\",\n        filename: \"route\",\n        bundlePath: \"app/api/produkty-direct/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Videos\\\\AIB_RAPORT\\\\production-reports\\\\src\\\\app\\\\api\\\\produkty-direct\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_produkcja_Videos_AIB_RAPORT_production_reports_src_app_api_produkty_direct_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprodukty-direct%2Froute&page=%2Fapi%2Fprodukty-direct%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprodukty-direct%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/produkty-direct/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/produkty-direct/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET() {\n    try {\n        const Database = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n        const path = __webpack_require__(/*! path */ \"path\");\n        const possiblePaths = [\n            path.join(process.cwd(), \"baza_danych.db\"),\n            path.join(process.cwd(), \"production-reports\", \"baza_danych.db\"),\n            \"./baza_danych.db\",\n            \"baza_danych.db\"\n        ];\n        let db;\n        for (const dbPath of possiblePaths){\n            try {\n                db = new Database(dbPath, {\n                    readonly: true\n                });\n                break;\n            } catch (error) {\n            // Continue to next path\n            }\n        }\n        if (!db) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Could not connect to database\"\n            }, {\n                status: 500\n            });\n        }\n        const produkty = db.prepare(\"SELECT * FROM produkty ORDER BY kod_handlowy ASC\").all();\n        db.close();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(produkty);\n    } catch (error) {\n        console.error(\"Direct database error:\", error);\n        const errorMessage = error instanceof Error ? error.message : \"An unknown error occurred\";\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Database error: \" + errorMessage\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/produkty-direct/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fprodukty-direct%2Froute&page=%2Fapi%2Fprodukty-direct%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fprodukty-direct%2Froute.ts&appDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cprodukcja%5CVideos%5CAIB_RAPORT%5Cproduction-reports&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();