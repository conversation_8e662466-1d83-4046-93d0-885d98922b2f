try {
  const Database = require("better-sqlite3");
  const path = require("path");

  const dbPath = path.join(__dirname, "baza_danych.db");
  console.log("Database path:", dbPath);

  const db = new Database(dbPath);

  console.log("=== Database Schema ===");
  const tableInfo = db.prepare("PRAGMA table_info(raporty_produkcyjne)").all();
  console.log("Columns in raporty_produkcyjne:");
  tableInfo.forEach((col) => {
    console.log(`- ${col.name}: ${col.type} (nullable: ${col.notnull === 0})`);
  });

  console.log("\n=== Recent Reports ===");
  const reports = db
    .prepare(
      `
    SELECT
      r.*,
      p.kod_handlowy,
      p.nazwa as produkt_nazwa
    FROM raporty_produkcyjne r
    LEFT JOIN produkty p ON r.produkt_id = p.id
    ORDER BY r.id DESC
    LIMIT 3
  `
    )
    .all();

  console.log(`Found ${reports.length} reports`);

  reports.forEach((report) => {
    console.log(`\nReport ID: ${report.id}`);
    console.log(`Product: ${report.kod_handlowy} - ${report.produkt_nazwa}`);
    console.log(`Metraz rolek: ${report.metraz_rolek}`);
    console.log(`Ilosc rolek: ${report.ilosc_rolek}`);
    console.log(
      `Calculated total: ${report.metraz_rolek * report.ilosc_rolek}`
    );
    console.log(`Nr zamowienia: ${report.nr_zamowienia}`);
    console.log(`Waga: ${report.waga}`);
    console.log(`Surowiec ID: ${report.surowiec_id}`);
    console.log(`Zuzyty surowiec: ${report.zuzyty_surowiec}`);
    console.log(`Szarza surowca: ${report.szarza_surowca}`);
    console.log(`Material ID: ${report.material_id}`);
    console.log(`Zuzyty material: ${report.zuzyty_material}`);
    console.log(`Maszyna ID: ${report.maszyna_id}`);
    console.log(`Pracownik ID: ${report.pracownik_id}`);
    console.log(`Czas pracy maszyny: ${report.czas_pracy_maszyny}`);
    console.log(`Czas pracy pracownika: ${report.czas_pracy_pracownika}`);
    console.log(`Created: ${report.data_utworzenia}`);
  });

  db.close();
  console.log("\nDatabase check completed successfully!");
} catch (error) {
  console.error("Error:", error.message);
  console.error("Stack:", error.stack);
}
